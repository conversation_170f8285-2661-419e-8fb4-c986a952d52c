提交：POST http://*************/cgi-bin/luci/ HTTP/1.1
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: max-age=0
Content-Length: 41
Content-Type: application/x-www-form-urlencoded
Host: *************
Origin: http://*************
Proxy-Connection: keep-alive
Referer: http://*************/cgi-bin/luci/
Upgrade-Insecure-Requests: 1
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

luci_username=root&luci_password=password


返回：
HTTP/1.1 302 Found
Cache-Control: no-cache, no-store, must-revalidate
Connection: Keep-Alive
Expires: 0
Keep-Alive: timeout=20
Location: /cgi-bin/luci/
Pragma: no-cache
Set-Cookie: sysauth_http=6777b2f7b470b8f2735b52178b873008; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly

