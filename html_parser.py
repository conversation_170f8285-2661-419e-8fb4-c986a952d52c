#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML解析器
专门用于解析OpenWrt Passwall2页面
"""

import re
import json
import html
from bs4 import BeautifulSoup

class PasswallHtmlParser:
    def __init__(self):
        pass
        
    def extract_token(self, html_content):
        """从HTML中提取token"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            token_input = soup.find('input', {'name': 'token'})
            if token_input:
                return token_input.get('value')
            return None
        except Exception as e:
            print(f"提取token失败: {e}")
            return None
            
    def extract_nodes(self, html_content, is_node_list_page=False):
        """从HTML中提取节点列表"""
        try:
            # 如果是节点列表页面，使用新的提取方法
            if is_node_list_page:
                nodes = self.extract_nodes_from_node_list(html_content)
                if nodes:
                    print(f"✅ 从节点列表页面成功提取到 {len(nodes)} 个节点")
                    return nodes

            # 首先尝试从data-ui-widget属性中提取
            nodes = self._extract_nodes_from_widget(html_content)
            if nodes:
                print(f"✅ 从data-ui-widget成功提取到 {len(nodes)} 个节点")
                # 保存节点数据到本地JSON文件
                self._save_nodes_to_json(nodes)
                return nodes

            # 如果失败，回退到原有方法
            print("⚠️ data-ui-widget提取失败，使用备用方法")
            soup = BeautifulSoup(html_content, 'html.parser')
            nodes = []

            # 查找所有的select元素
            select_elements = soup.find_all('select')

            for select in select_elements:
                select_name = select.get('name', '').lower()
                # 查找包含node的select元素
                if 'node' in select_name:
                    options = select.find_all('option')
                    for option in options:
                        value = option.get('value', '')
                        text = option.get_text(strip=True)
                        if value and text and value != '' and text != '请选择':
                            # 清理节点名称
                            clean_name = self._clean_node_name(text)
                            # 提取地区信息
                            region = self._extract_region_from_name(clean_name)
                            nodes.append({
                                'id': value,
                                'name': clean_name,
                                'region': region
                            })
                    break

            return nodes

        except Exception as e:
            print(f"提取节点列表失败: {e}")
            return []

    def extract_nodes_from_node_list(self, html_content):
        """从节点列表页面提取节点信息"""
        try:
            # 使用新的正则表达式匹配节点信息
            pattern = r"<input type='hidden' id='cbid\.passwall2\.(.*?)\.type' value='(.*?)'/>(.*?)<input type='hidden' id='cbid\.passwall2\..*?\.address' value='(.*?)'/><input type='hidden' id='cbid\.passwall2\..*?\.port' value='(.*?)'/>"

            matches = re.findall(pattern, html_content, re.DOTALL)

            if not matches:
                print("❌ 未找到节点信息")
                return []

            nodes = []
            for match in matches:
                node_id = match[0]
                node_type = match[1]
                node_name_html = match[2]
                node_address = match[3]
                node_port = match[4]

                # 从HTML片段中提取节点名称
                node_name = self._extract_node_name_from_html(node_name_html)

                # 提取地区信息
                region = self._extract_region_from_name(node_name)

                node_info = {
                    'id': node_id,
                    'type': node_type,
                    'name': node_name,
                    'address': node_address,
                    'port': node_port,
                    'region': region
                }

                nodes.append(node_info)

            print(f"✅ 成功提取到 {len(nodes)} 个节点")

            # 保存节点数据到本地JSON文件
            self._save_nodes_to_json(nodes)

            return nodes

        except Exception as e:
            print(f"❌ 从节点列表页面提取节点失败: {e}")
            return []

    def _extract_node_name_from_html(self, html_fragment):
        """从HTML片段中提取节点名称"""
        try:
            # 尝试从各种可能的HTML元素中提取节点名称
            # 首先尝试从value属性中提取
            name_pattern = r"value='([^']*?)'"
            name_match = re.search(name_pattern, html_fragment)
            if name_match:
                raw_name = name_match.group(1)
                return self._clean_node_name(raw_name)

            # 如果没有找到，尝试从文本内容中提取
            # 移除HTML标签
            clean_text = re.sub(r'<[^>]+>', '', html_fragment).strip()
            if clean_text:
                return self._clean_node_name(clean_text)

            return "未知节点"

        except Exception as e:
            print(f"❌ 提取节点名称失败: {e}")
            return "未知节点"

    def _clean_node_name(self, raw_name):
        """清理节点名称，去掉前缀和乱码"""
        try:
            if not raw_name:
                return "未知节点"

            # 去掉常见的前缀
            prefixes_to_remove = [
                "Xray Trojan：",
                "Xray Trojanï¼",
                "V2ray Trojan：",
                "V2ray Trojanï¼",
                "Trojan：",
                "Trojanï¼",
                "Xray：",
                "Xrayï¼",
                "V2ray：",
                "V2rayï¼"
            ]

            cleaned_name = raw_name
            for prefix in prefixes_to_remove:
                if cleaned_name.startswith(prefix):
                    cleaned_name = cleaned_name[len(prefix):].strip()
                    break

            # 去掉各种乱码字符和特殊符号
            # 1. 去掉损坏的emoji编码 (如 ð­ð°)
            cleaned_name = re.sub(r'ð[^\s\w]*', '', cleaned_name)

            # 2. 去掉其他常见乱码字符
            cleaned_name = re.sub(r'[ï¼ï½ï¾ï¿]+', '', cleaned_name)

            # 3. 去掉特殊符号（如 º、µ、°、±、²、³、¹、¼、½、¾、×、÷ 等）
            special_symbols = [
                'º', 'µ', '°', '±', '²', '³', '¹', '¼', '½', '¾', '×', '÷',
                'ª', '«', '»', '¬', '®', '¯', '´', '¸', '¿', 'À', 'Á', 'Â', 'Ã',
                'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï',
                'Ð', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'Ù', 'Ú', 'Û', 'Ü',
                'Ý', 'Þ', 'ß', 'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è',
                'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ð', 'ñ', 'ò', 'ó', 'ô',
                'õ', 'ö', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'þ', 'ÿ'
            ]

            for symbol in special_symbols:
                cleaned_name = cleaned_name.replace(symbol, '')

            # 4. 去掉开头的特殊符号，但保留正常的emoji、中文、字母、数字、方括号
            cleaned_name = re.sub(r'^[^\w\s\u4e00-\u9fff\U0001F1E6-\U0001F1FF\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F700-\U0001F77F\U0001F780-\U0001F7FF\U0001F800-\U0001F8FF\U0001F900-\U0001F9FF\U0001FA00-\U0001FA6F\U0001FA70-\U0001FAFF\[\]]+', '', cleaned_name)

            # 5. 清理多余的空格
            cleaned_name = re.sub(r'\s+', ' ', cleaned_name).strip()

            # 如果名称为空，返回默认值
            if not cleaned_name.strip():
                return "未知节点"

            return cleaned_name.strip()

        except Exception as e:
            print(f"❌ 清理节点名称失败: {e}")
            return raw_name if raw_name else "未知节点"

    def _extract_nodes_from_widget(self, html_content):
        """从data-ui-widget属性中提取节点信息（备用方法）"""
        try:
            # 查找data-ui-widget属性
            pattern = r'data-ui-widget="([^"]+)"'
            match = re.search(pattern, html_content)

            if not match:
                print("❌ 未找到data-ui-widget属性")
                return []

            # 获取属性值并解码HTML实体
            widget_data = html.unescape(match.group(1))
            print(f"🔍 找到data-ui-widget: {widget_data[:100]}...")

            # 解析JSON数据
            try:
                json_data = json.loads(widget_data)
                if len(json_data) < 3:
                    print("❌ data-ui-widget格式不正确")
                    return []

                # 第三个元素包含节点映射
                nodes_dict = json_data[2]
                nodes = []

                for node_id, node_name in nodes_dict.items():
                    if node_id != "nil":  # 跳过"关闭"选项
                        # 清理节点名称
                        clean_name = self._clean_node_name(node_name)
                        region = self._extract_region_from_name(clean_name)
                        nodes.append({
                            'id': node_id,
                            'name': clean_name,
                            'region': region
                        })

                return nodes

            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return []

        except Exception as e:
            print(f"❌ 从data-ui-widget提取节点失败: {e}")
            return []

    def _save_nodes_to_json(self, nodes):
        """保存节点数据到本地JSON文件"""
        try:
            import datetime

            # 统计节点类型和地区分布
            type_count = {}
            region_count = {}

            for node in nodes:
                # 统计节点类型
                node_type = node.get('type', '未知')
                type_count[node_type] = type_count.get(node_type, 0) + 1

                # 统计地区分布
                region = node.get('region', '未知')
                region_count[region] = region_count.get(region, 0) + 1

            data = {
                'timestamp': datetime.datetime.now().isoformat(),
                'total_nodes': len(nodes),
                'statistics': {
                    'by_type': type_count,
                    'by_region': region_count
                },
                'nodes': nodes
            }

            with open('nodes_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            print(f"💾 节点数据已保存到 nodes_data.json，共 {len(nodes)} 个节点")
            print(f"📊 节点类型分布: {type_count}")
            print(f"🌍 地区分布: {region_count}")

        except Exception as e:
            print(f"❌ 保存节点数据失败: {e}")

    def extract_existing_socks(self, html_content):
        """从HTML中提取现有的Socks配置"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            existing_configs = []
            
            # 查找所有包含删除操作的表单元素
            # 根据您提供的删除包信息，查找类似 cbi.rts.passwall2.xxx 的项
            delete_inputs = soup.find_all('input', {'name': re.compile(r'cbi\.rts\.passwall2\.')})
            for input_elem in delete_inputs:
                config_id = input_elem.get('name').replace('cbi.rts.passwall2.', '')
                existing_configs.append(config_id)
                
            # 也可以通过查找表格行来获取配置信息
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    # 查找包含Socks配置的行
                    cells = row.find_all(['td', 'th'])
                    for cell in cells:
                        if 'socks' in cell.get_text().lower():
                            # 尝试提取配置ID
                            buttons = cell.find_all('button')
                            for button in buttons:
                                onclick = button.get('onclick', '')
                                if 'delete' in onclick.lower():
                                    # 从onclick事件中提取ID
                                    id_match = re.search(r'[a-zA-Z0-9]{8}', onclick)
                                    if id_match:
                                        config_id = id_match.group()
                                        if config_id not in existing_configs:
                                            existing_configs.append(config_id)
                                            
            return existing_configs
            
        except Exception as e:
            print(f"提取现有Socks配置失败: {e}")
            return []

    def extract_token(self, html_content):
        """从HTML中提取token"""
        try:
            # 查找token的hidden input
            pattern = r'<input[^>]*name="token"[^>]*value="([^"]+)"[^>]*>'
            match = re.search(pattern, html_content)

            if match:
                token = match.group(1)
                print(f"🔑 成功提取token: {token}")
                return token
            else:
                print("❌ 未找到token")
                return None

        except Exception as e:
            print(f"❌ 提取token失败: {e}")
            return None

    def extract_existing_socks_configs(self, html_content):
        """从HTML中提取已存在的socks配置"""
        try:
            # 使用正则表达式查找socks配置链接
            pattern = r'/cgi-bin/luci/admin/services/passwall2/socks_config/(.*?)\'"'
            matches = re.findall(pattern, html_content)

            socks_configs = []
            for match in matches:
                socks_configs.append(match)

            print(f"🔍 找到 {len(socks_configs)} 个已存在的socks配置:")
            for i, config_id in enumerate(socks_configs, 1):
                print(f"  {i}. {config_id}")

            return socks_configs

        except Exception as e:
            print(f"❌ 提取socks配置失败: {e}")
            return []

    def _extract_region_from_name(self, name):
        """从节点名称中提取地区信息"""
        region_keywords = {
            '香港': ['香港', 'HK', 'Hong Kong', 'hongkong', '港'],
            '美国': ['美国', 'US', 'USA', 'United States', 'america', '美'],
            '日本': ['日本', 'JP', 'Japan', 'tokyo', '日'],
            '新加坡': ['新加坡', 'SG', 'Singapore', '新'],
            '韩国': ['韩国', 'KR', 'Korea', 'seoul', '韩'],
            '台湾': ['台湾', 'TW', 'Taiwan', '台'],
            '英国': ['英国', 'UK', 'Britain', 'london', '英'],
            '德国': ['德国', 'DE', 'Germany', 'berlin', '德'],
            '法国': ['法国', 'FR', 'France', 'paris', '法'],
            '加拿大': ['加拿大', 'CA', 'Canada', '加'],
            '澳大利亚': ['澳大利亚', 'AU', 'Australia', '澳'],
            '荷兰': ['荷兰', 'NL', 'Netherlands', '荷'],
            '瑞士': ['瑞士', 'CH', 'Switzerland', '瑞'],
            '俄罗斯': ['俄罗斯', 'RU', 'Russia', '俄'],
            '印度': ['印度', 'IN', 'India', '印'],
            '泰国': ['泰国', 'TH', 'Thailand', '泰'],
            '马来西亚': ['马来西亚', 'MY', 'Malaysia', '马来'],
            '菲律宾': ['菲律宾', 'PH', 'Philippines', '菲'],
            '越南': ['越南', 'VN', 'Vietnam', '越'],
            '印尼': ['印尼', 'ID', 'Indonesia', '印尼'],
        }
        
        name_upper = name.upper()
        for region, keywords in region_keywords.items():
            for keyword in keywords:
                if keyword.upper() in name_upper:
                    return region
                    
        return '其他'
        
    def extract_session_id(self, cookie_header):
        """从Cookie头中提取session ID"""
        try:
            session_match = re.search(r'sysauth_http=([^;]+)', cookie_header)
            if session_match:
                return session_match.group(1)
            return None
        except Exception as e:
            print(f"提取session ID失败: {e}")
            return None
            
    def extract_config_id_from_redirect(self, location_header):
        """从重定向URL中提取配置ID"""
        try:
            config_id_match = re.search(r'/socks_config/([^/]+)$', location_header)
            if config_id_match:
                return config_id_match.group(1)
            return None
        except Exception as e:
            print(f"提取配置ID失败: {e}")
            return None
