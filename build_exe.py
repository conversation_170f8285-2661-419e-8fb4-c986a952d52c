#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将OpenWrt Passwall Socks批量配置工具打包成exe
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build_dirs():
    """清理之前的构建目录"""
    print("🧹 清理之前的构建文件...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"  ✅ 已删除 {dir_name} 目录")
            except Exception as e:
                print(f"  ⚠️ 删除 {dir_name} 失败: {e}")
    
    # 清理spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        try:
            os.remove(spec_file)
            print(f"  ✅ 已删除 {spec_file}")
        except Exception as e:
            print(f"  ⚠️ 删除 {spec_file} 失败: {e}")

def check_dependencies():
    """检查依赖是否安装"""
    print("📦 检查依赖...")

    # 检查包的映射关系
    package_imports = {
        'requests': 'requests',
        'beautifulsoup4': 'bs4',
        'lxml': 'lxml',
        'pyinstaller': 'PyInstaller'
    }

    missing_packages = []

    for package, import_name in package_imports.items():
        try:
            __import__(import_name)
            print(f"  ✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} 未安装")

    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False

    return True

def build_exe():
    """使用PyInstaller打包exe"""
    print("🔨 开始打包exe...")
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个exe文件
        '--windowed',                   # 不显示控制台窗口
        '--name=OpenWrt_Passwall_Socks_配置工具',  # exe文件名
        '--icon=icon.ico',              # 图标文件（如果存在）
        '--add-data=ui_config.py;.',    # 包含配置文件
        '--add-data=config.py;.',       # 包含配置文件
        '--add-data=html_parser.py;.',  # 包含解析器
        '--add-data=openwrt_client.py;.',  # 包含客户端
        '--add-data=logger.py;.',       # 包含日志模块
        '--hidden-import=tkinter',      # 确保tkinter被包含
        '--hidden-import=tkinter.ttk',  # 确保ttk被包含
        '--hidden-import=requests',     # 确保requests被包含
        '--hidden-import=bs4',          # 确保beautifulsoup4被包含
        '--hidden-import=lxml',         # 确保lxml被包含
        '--clean',                      # 清理临时文件
        'main.py'                       # 主程序文件
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists('icon.ico'):
        cmd = [arg for arg in cmd if not arg.startswith('--icon')]
        print("  ℹ️ 未找到icon.ico，将使用默认图标")
    
    print(f"  执行命令: {' '.join(cmd)}")
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("  ✅ 打包成功！")
            return True
        else:
            print("  ❌ 打包失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"  ❌ 打包过程中发生异常: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到dist目录"""
    print("📁 复制额外文件...")
    
    if not os.path.exists('dist'):
        print("  ⚠️ dist目录不存在，跳过文件复制")
        return
    
    # 要复制的文件列表
    files_to_copy = [
        'README.md',
        'UI配置优化说明.md',
        '特殊符号清理优化说明.md',
        'requirements.txt'
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, 'dist/')
                print(f"  ✅ 已复制 {file_name}")
            except Exception as e:
                print(f"  ⚠️ 复制 {file_name} 失败: {e}")
        else:
            print(f"  ℹ️ {file_name} 不存在，跳过")

def create_readme():
    """创建使用说明"""
    print("📝 创建使用说明...")
    
    readme_content = """# OpenWrt Passwall Socks 批量配置工具

## 📋 使用说明

### 🚀 快速开始
1. 双击运行 `OpenWrt_Passwall_Socks_配置工具.exe`
2. 根据需要调整配置选项
3. 点击"开始批量配置"按钮

### ⚙️ 配置说明

#### 处理模式
- **限制模式**: 只处理前N个节点，适合测试
- **分批模式**: 将所有节点分批处理，适合大量节点

#### 调试文件选项
- 默认情况下不保存调试文件，节省磁盘空间
- 如需调试问题，可勾选相应的调试文件保存选项

#### 二次登录重新提交
- 当批量提交失败时，自动退出登录重新登录再试一次
- 建议保持开启以提高成功率

### 🔧 自定义配置

如需修改默认设置，请：
1. 解压程序到文件夹
2. 编辑 `ui_config.py` 文件
3. 修改对应方法的返回值
4. 重新运行程序

详细配置说明请参考 `UI配置优化说明.md`

### 📞 技术支持

如遇到问题，请检查：
1. OpenWrt路由器是否正常运行
2. 网络连接是否正常
3. Passwall2插件是否已安装

### 📄 版本信息

- 版本: 1.0.0
- 构建时间: """ + str(Path(__file__).stat().st_mtime) + """
- Python版本: """ + sys.version + """

---
© 2024 OpenWrt Passwall Socks 批量配置工具
"""
    
    try:
        with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("  ✅ 已创建使用说明.txt")
    except Exception as e:
        print(f"  ⚠️ 创建使用说明失败: {e}")

def main():
    """主函数"""
    print("🚀 OpenWrt Passwall Socks 配置工具 - 打包脚本")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("❌ 错误: 未找到main.py文件，请在项目根目录运行此脚本")
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 打包exe
    if not build_exe():
        return False
    
    # 复制额外文件
    copy_additional_files()
    
    # 创建使用说明
    create_readme()
    
    print("\n" + "=" * 60)
    print("🎉 打包完成！")
    print(f"📁 输出目录: {os.path.abspath('dist')}")
    
    # 检查生成的文件
    if os.path.exists('dist'):
        files = os.listdir('dist')
        print("📄 生成的文件:")
        for file in files:
            file_path = os.path.join('dist', file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                print(f"  - {file} ({size:.1f} MB)")
    
    print("\n💡 提示:")
    print("  - 可以直接运行 dist 目录中的 exe 文件")
    print("  - 如需分发，可以将整个 dist 目录打包")
    print("  - 首次运行可能需要一些时间来解压")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按回车键退出...")
        sys.exit(1)
    else:
        input("\n按回车键退出...")
