#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试节点管理功能
验证新的智能节点信息管理逻辑
"""

import json
import os
import shutil
from openwrt_client import OpenWrtClient

def backup_original_data():
    """备份原始数据"""
    if os.path.exists('nodes_data.json'):
        shutil.copy2('nodes_data.json', 'nodes_data_backup.json')
        print("✅ 已备份原始 nodes_data.json")

def restore_original_data():
    """恢复原始数据"""
    if os.path.exists('nodes_data_backup.json'):
        shutil.copy2('nodes_data_backup.json', 'nodes_data.json')
        print("✅ 已恢复原始 nodes_data.json")

def create_test_existing_data():
    """创建测试用的现有节点数据"""
    test_data = [
        {
            "id": "old_hk_01",
            "type": "Xray", 
            "name": "Hong Kong 01",
            "address": "old.server1.com",
            "port": "443",
            "region": "香港",
            "socks_port": 10001,
            "is_use": True
        },
        {
            "id": "old_hk_02",
            "type": "Xray",
            "name": "Hong Kong 02", 
            "address": "old.server2.com",
            "port": "443",
            "region": "香港",
            "socks_port": 10002,
            "is_use": True
        },
        {
            "id": "old_us_01",
            "type": "Xray",
            "name": "USA Node 01",
            "address": "old.usa1.com", 
            "port": "443",
            "region": "美国",
            "socks_port": 20001,
            "is_use": True
        },
        {
            "id": "old_jp_01",
            "type": "Xray",
            "name": "Japan Node 01",
            "address": "old.japan1.com",
            "port": "443", 
            "region": "日本",
            "socks_port": 30001,
            "is_use": True
        }
    ]
    
    with open('nodes_data.json', 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print("✅ 已创建测试用现有节点数据")
    return test_data

def create_test_new_nodes():
    """创建测试用的新获取节点数据"""
    new_nodes = [
        {
            "id": "new_hk_01",
            "type": "Xray",
            "name": "Hong Kong 01",  # 已存在，应该更新
            "address": "new.server1.com",  # 新地址
            "port": "8443",  # 新端口
            "region": "香港"
        },
        {
            "id": "new_hk_03", 
            "type": "Xray",
            "name": "Hong Kong 03",  # 新增节点
            "address": "new.server3.com",
            "port": "443",
            "region": "香港"
        },
        {
            "id": "new_us_01",
            "type": "Xray", 
            "name": "USA Node 01",  # 已存在，应该更新
            "address": "new.usa1.com",  # 新地址
            "port": "9443",  # 新端口
            "region": "美国"
        },
        {
            "id": "new_sg_01",
            "type": "Xray",
            "name": "Singapore 01",  # 新增节点，新地区
            "address": "new.singapore1.com",
            "port": "443",
            "region": "新加坡"
        }
    ]
    
    print("✅ 已创建测试用新获取节点数据")
    return new_nodes

def test_node_management():
    """测试节点管理功能"""
    print("🧪 开始测试节点管理功能")
    print("=" * 60)
    
    # 备份原始数据
    backup_original_data()
    
    try:
        # 创建测试数据
        existing_data = create_test_existing_data()
        new_nodes = create_test_new_nodes()
        
        print(f"\n📊 测试场景:")
        print(f"  现有节点: {len(existing_data)} 个")
        print(f"  新获取节点: {len(new_nodes)} 个")
        print(f"  预期结果:")
        print(f"    - Hong Kong 01: 更新地址和端口，保持socks_port=10001")
        print(f"    - Hong Kong 03: 新增，分配socks_port=10003")
        print(f"    - USA Node 01: 更新地址和端口，保持socks_port=20001")
        print(f"    - Singapore 01: 新增，分配socks_port=40001")
        print(f"    - Hong Kong 02: 废弃，设置is_use=false")
        print(f"    - Japan Node 01: 废弃，设置is_use=false")
        
        # 创建客户端并测试
        client = OpenWrtClient("192.168.100.5", "root", "password")
        
        print(f"\n🔄 执行节点管理...")
        result_nodes = client.assign_ports_to_nodes(new_nodes)
        
        print(f"\n📋 处理结果:")
        print(f"  返回的可用节点: {len(result_nodes)} 个")
        
        # 检查结果
        print(f"\n🔍 详细结果检查:")
        for node in result_nodes:
            print(f"  ✅ {node['name']} -> 端口 {node['socks_port']} (is_use: {node.get('is_use', True)})")
        
        # 检查完整的nodes_data.json
        with open('nodes_data.json', 'r', encoding='utf-8') as f:
            all_nodes = json.load(f)
        
        print(f"\n📄 完整的nodes_data.json ({len(all_nodes)} 个节点):")
        for node in all_nodes:
            status = "✅ 启用" if node.get('is_use', True) else "❌ 停用"
            print(f"  {status} {node['name']} -> 端口 {node['socks_port']}")
        
        # 验证结果
        print(f"\n🎯 验证结果:")
        
        # 检查更新的节点
        hk01 = next((n for n in all_nodes if n['name'] == 'Hong Kong 01'), None)
        if hk01:
            if hk01['address'] == 'new.server1.com' and hk01['port'] == '8443' and hk01['socks_port'] == 10001:
                print(f"  ✅ Hong Kong 01 更新正确")
            else:
                print(f"  ❌ Hong Kong 01 更新错误")
        
        # 检查新增的节点
        hk03 = next((n for n in all_nodes if n['name'] == 'Hong Kong 03'), None)
        if hk03 and hk03['socks_port'] == 10003:
            print(f"  ✅ Hong Kong 03 新增正确")
        else:
            print(f"  ❌ Hong Kong 03 新增错误")
        
        sg01 = next((n for n in all_nodes if n['name'] == 'Singapore 01'), None)
        if sg01 and sg01['socks_port'] == 40001:
            print(f"  ✅ Singapore 01 新增正确")
        else:
            print(f"  ❌ Singapore 01 新增错误")
        
        # 检查废弃的节点
        hk02 = next((n for n in all_nodes if n['name'] == 'Hong Kong 02'), None)
        if hk02 and not hk02.get('is_use', True):
            print(f"  ✅ Hong Kong 02 废弃标记正确")
        else:
            print(f"  ❌ Hong Kong 02 废弃标记错误")
        
        jp01 = next((n for n in all_nodes if n['name'] == 'Japan Node 01'), None)
        if jp01 and not jp01.get('is_use', True):
            print(f"  ✅ Japan Node 01 废弃标记正确")
        else:
            print(f"  ❌ Japan Node 01 废弃标记错误")
        
        print(f"\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 恢复原始数据
        restore_original_data()

if __name__ == "__main__":
    test_node_management()
