#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
包含应用程序的各种配置参数
"""

class Config:
    # 默认连接参数（从登录包.txt提取）
    DEFAULT_HOST = "*************"
    DEFAULT_USERNAME = "root"
    DEFAULT_PASSWORD = "password"
    
    # 地区端口映射 - 新的端口分配策略（确保所有端口 < 70000）
    REGION_PORTS = {
        "香港": 10000,      # 10001-19999 (最大9999个节点)
        "美国": 20000,      # 20001-29999 (最大9999个节点)
        "日本": 30000,      # 30001-39999 (最大9999个节点)
        "新加坡": 40000,    # 40001-49999 (最大9999个节点)
        "台湾": 50000,      # 50001-59999 (最大9999个节点)
        # 其他地区统一分配到60001-69999范围（共享，不能重复）
        "韩国": 60000,      # 60001-69999 (与其他地区共享)
        "英国": 60000,      # 60001-69999 (与其他地区共享)
        "德国": 60000,      # 60001-69999 (与其他地区共享)
        "法国": 60000,      # 60001-69999 (与其他地区共享)
        "加拿大": 60000,    # 60001-69999 (与其他地区共享)
        "澳大利亚": 60000,  # 60001-69999 (与其他地区共享)
        "荷兰": 60000,      # 60001-69999 (与其他地区共享)
        "瑞士": 60000,      # 60001-69999 (与其他地区共享)
        "俄罗斯": 60000,    # 60001-69999 (与其他地区共享)
        "印度": 60000,      # 60001-69999 (与其他地区共享)
        "泰国": 60000,      # 60001-69999 (与其他地区共享)
        "马来西亚": 60000,  # 60001-69999 (与其他地区共享)
        "菲律宾": 60000,    # 60001-69999 (与其他地区共享)
        "越南": 60000,      # 60001-69999 (与其他地区共享)
        "印尼": 60000,      # 60001-69999 (与其他地区共享)
    }

    # 端口范围限制
    PORT_LIMITS = {
        "香港": {"min": 10001, "max": 19999},
        "美国": {"min": 20001, "max": 29999},
        "日本": {"min": 30001, "max": 39999},
        "新加坡": {"min": 40001, "max": 49999},
        "台湾": {"min": 50001, "max": 59999},
        "韩国": {"min": 60001, "max": 69999},
        "其他": {"min": 60001, "max": 69999}  # 其他地区共享60001-69999范围
    }

    # 全局端口上限
    MAX_SOCKS_PORT = 69999
    
    # 默认端口（未知地区使用）
    DEFAULT_PORT = 1080
    
    # 请求超时时间（秒）
    REQUEST_TIMEOUT = 30
    
    # 请求间隔时间（秒）
    REQUEST_INTERVAL = 0.5
    
    # 最大重试次数
    MAX_RETRIES = 3

# 为了兼容性，创建REGION_PORT_MAP别名
REGION_PORT_MAP = Config.REGION_PORTS
