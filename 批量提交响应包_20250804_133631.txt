批量提交响应结果:
==================================================
状态码: 200
状态: OK
配置数量: 1
配置ID列表: 3hhWmggO
==================================================
响应头:
Connection: Keep-Alive
Transfer-Encoding: chunked
Keep-Alive: timeout=20
X-CBI-State: 2
Content-Type: text/html
Cache-Control: no-cache
Expires: 0
X-Frame-Options: SAMEORIGIN
X-XSS-Protection: 1; mode=block
X-Content-Type-Options: nosniff

响应内容:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/luci-static/argon/icon/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/luci-static/argon/icon/apple-icon-144x144.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/luci-static/argon/icon/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/luci-static/argon/icon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/luci-static/argon/icon/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/luci-static/argon/icon/favicon-16x16.png">
    <link rel="manifest" href="/luci-static/argon/icon/manifest.json"  crossorigin="use-credentials">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="msapplication-TileImage" content="/luci-static/argon/icon/ms-icon-144x144.png">
    <meta name="theme-color" content="#5e72e4">
    <link rel="stylesheet" href="/luci-static/argon/css/cascade.css?v=*********">
    <style title="text/css">
        
        :root {
            --primary: #5e72e4;
            --dark-primary: #483d8b;
            --blur-radius:10px;
            --blur-opacity:0.5;
            --blur-radius-dark:10px;
            --blur-opacity-dark:0.5;
        }
        </style>
	<link rel="shortcut icon" href="/luci-static/argon/favicon.ico">
<link rel="stylesheet" href="/luci-static/resources/easepi/easeicon.css?t=1649313193968">
	<script src="/luci-static/argon/js/polyfill.min.js?v=*********"></script>
	<script src="/cgi-bin/luci/admin/translations/en?v=git-24.339.46321-5f9267c"></script>
	<script src="/luci-static/resources/cbi.js?v=git-24.339.46321-5f9267c"></script>
	<script src="/luci-static/resources/luci.js?v=git-24.339.46321-5f9267c"></script>
	<script src="/luci-static/argon/js/jquery.min.js?v=3.5.1"></script>
</head>

<body
	theme="light"
	class="lang_en Basic Settings logged-in"
	data-page="admin-services-passwall2-settings">

	<div class="main">
		<div class="main-left" id="mainmenu" style="display:none">
			<div class="sidenav-header d-flex align-items-center">
				<a class="brand" href="#">iStoreOS</a>
				<div class="ml-auto">
					<!-- Sidenav toggler -->
					<div class="sidenav-toggler d-none d-xl-block active" data-action="sidenav-unpin"
						data-target="#sidenav-main">
						<div class="sidenav-toggler-inner">
							<i class="sidenav-toggler-line"></i>
							<i class="sidenav-toggler-line"></i>
							<i class="sidenav-toggler-line"></i>
						</div>
					</div>
				</div>
			</div>
			<div style="display: flex; flex-direction: column;position: relative;align-items: center;">
				<input id="menu_search" type="text" placeholder="Search Menu">
				<div id="search_result" style="display: none;">
					<ul class="float-list" style="width: 90%;min-height: 10em;max-height: 20em;"></ul>
				</div>
			</div>
		</div>
		<div class="main-right">
			<header class="bg-primary">
				<div class="fill">
					<div class="container">
						<div class="flex1">
							<a class="showSide"></a>
							<a class="brand" href="#">iStoreOS</a>
						</div>
						<div class="status" id="indicators"></div>
					</div>
				</div>
			</header>
			<div class="darkMask"></div>
			<div id="maincontent">
				<div class="container"><noscript>
						<div class="alert-message error">
							<h4>JavaScript required!</h4>
							<p>You must enable JavaScript in your browser or LuCI will not work properly.</p>
						</div>
					</noscript>

					<div id="tabmenu" style="display:none"></div>


<script type="text/javascript" src="/luci-static/resources/promis.min.js?v=git-24.339.46321-5f9267c"></script>
<script type="text/javascript" src="/luci-static/resources/luci.js?v=git-24.339.46321-5f9267c"></script>
<script type="text/javascript">
	L = new LuCI({"apply_rollback":30,"resource":"\/luci-static\/resources","media":"\/luci-static\/argon","documentroot":"\/www","pathinfo":"\/admin\/services\/passwall2\/settings","apply_display":1.5,"requestpath":["admin","services","passwall2","settings"],"ubuspath":"\/ubus\/","scriptname":"\/cgi-bin\/luci","dispatchpath":["admin","services","passwall2","settings"],"sessionid":"2d662ff64dd367970cc105e9e53650b2","token":"d517cfdb3b04b2fc961dcd7a6efccea2","apply_timeout":5,"apply_holdoff":2,"nodespec":{"satisfied":true,"action":{"post":{"cbi.submit":true},"type":"cbi","path":"passwall2\/client\/global"},"order":1,"readonly":false,"title":"Basic Settings"},"pollinterval":5});
</script>




<form method="post" name="cbi" action="/cgi-bin/luci/admin/services/passwall2/settings" enctype="multipart/form-data" onreset="return cbi_validate_reset(this)" onsubmit="return cbi_validate_form(this, 'Some fields are invalid, cannot save values!')" data-strings="{&#34;path&#34;:{&#34;resource&#34;:&#34;\/luci-static\/resources&#34;,&#34;browser&#34;:&#34;\/cgi-bin\/luci\/admin\/filebrowser&#34;},&#34;label&#34;:{&#34;choose&#34;:&#34;-- Please choose --&#34;,&#34;custom&#34;:&#34;-- custom --&#34;}}">
	<div>
		<input type="hidden" name="token" value="d517cfdb3b04b2fc961dcd7a6efccea2" />
		<input type="hidden" name="cbi.submit" value="1" />
		<input type="submit" value="Save" class="hidden" />
	</div>

<div class="cbi-map" id="cbi-passwall2">
	
	
	<style>
/*!
Pure v1.0.1
Copyright 2013 Yahoo!
Licensed under the BSD License.
https://github.com/pure-css/pure/blob/master/LICENSE.md
*/
.pure-g{letter-spacing:-.31em;text-rendering:optimizespeed;font-family:FreeSans,Arimo,"Droid Sans",Helvetica,Arial,sans-serif;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-flow:row wrap;-ms-flex-flow:row wrap;flex-flow:row wrap;-webkit-align-content:flex-start;align-items: center;-ms-flex-line-pack:start;align-content:flex-start}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active) {table .pure-g{display:block}}.opera-only :-o-prefocus,.pure-g{word-spacing:-.43em}.pure-u{display:inline-block;zoom:1;letter-spacing:normal;word-spacing:normal;vertical-align:top;text-rendering:auto}.pure-g [class*=pure-u]{font-family:sans-serif}.pure-u-1,.pure-u-1-1,.pure-u-1-12,.pure-u-1-2,.pure-u-1-24,.pure-u-1-3,.pure-u-1-4,.pure-u-1-5,.pure-u-1-6,.pure-u-1-8,.pure-u-10-24,.pure-u-11-12,.pure-u-11-24,.pure-u-12-24,.pure-u-13-24,.pure-u-14-24,.pure-u-15-24,.pure-u-16-24,.pure-u-17-24,.pure-u-18-24,.pure-u-19-24,.pure-u-2-24,.pure-u-2-3,.pure-u-2-5,.pure-u-20-24,.pure-u-21-24,.pure-u-22-24,.pure-u-23-24,.pure-u-24-24,.pure-u-3-24,.pure-u-3-4,.pure-u-3-5,.pure-u-3-8,.pure-u-4-24,.pure-u-4-5,.pure-u-5-12,.pure-u-5-24,.pure-u-5-5,.pure-u-5-6,.pure-u-5-8,.pure-u-6-24,.pure-u-7-12,.pure-u-7-24,.pure-u-7-8,.pure-u-8-24,.pure-u-9-24{display:inline-block;zoom:1;letter-spacing:normal;word-spacing:normal;vertical-align:top;text-rendering:auto}.pure-u-1-24{width:4.1667%}.pure-u-1-12,.pure-u-2-24{width:8.3333%}.pure-u-1-8,.pure-u-3-24{width:12.5%}.pure-u-1-6,.pure-u-4-24{width:16.6667%}.pure-u-1-5{width:20%}.pure-u-5-24{width:20.8333%}.pure-u-1-4,.pure-u-6-24{width:25%}.pure-u-7-24{width:29.1667%}.pure-u-1-3,.pure-u-8-24{width:33.3333%}.pure-u-3-8,.pure-u-9-24{width:37.5%}.pure-u-2-5{width:40%}.pure-u-10-24,.pure-u-5-12{width:41.6667%}.pure-u-11-24{width:45.8333%}.pure-u-1-2,.pure-u-12-24{width:50%}.pure-u-13-24{width:54.1667%}.pure-u-14-24,.pure-u-7-12{width:58.3333%}.pure-u-3-5{width:60%}.pure-u-15-24,.pure-u-5-8{width:62.5%}.pure-u-16-24,.pure-u-2-3{width:66.6667%}.pure-u-17-24{width:70.8333%}.pure-u-18-24,.pure-u-3-4{width:75%}.pure-u-19-24{width:79.1667%}.pure-u-4-5{width:80%}.pure-u-20-24,.pure-u-5-6{width:83.3333%}.pure-u-21-24,.pure-u-7-8{width:87.5%}.pure-u-11-12,.pure-u-22-24{width:91.6667%}.pure-u-23-24{width:95.8333%}.pure-u-1,.pure-u-1-1,.pure-u-24-24,.pure-u-5-5{width:100%}
	.block {
		margin: 0.5rem;
		padding: 0;
		font-weight: normal;
		font-style: normal;
		line-height: 1;
		font-family: inherit;
		min-width: inherit;
		overflow-x: auto;
		overflow-y: hidden;
		border: 1px solid rgba(0,0,0,.05);
		border-radius: .375rem;
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	}
	.img-con {
		margin: 1rem;

	}
	.green {
		font-size:.9rem;
		color: #2dce89;
	}
	.red {
		font-size:.9rem;
		color: #fb6340;
	}
	.yellow {
		font-size:.9rem;
		color: #fb9a05;
	}
	.block img {
		width: 48px;
		height: auto;
		/* float:right; */
	}
	.block h4 {
		font-size: .8125rem;
		font-weight: 600;
		margin: 1rem 0rem 1rem 1rem;
		color:#8898aa!important;
		line-height: 1.8em;
		min-height: 48px;
	}
	
	.check {
		cursor: pointer;
	}

	@media screen and (max-width: 720px) {
		.block {
			margin: 0.2rem;
		}
		.pure-u-1-4 {
			width: 50%;
		}
		.pure-u-1-2 {
			width: 100%;
		}
		.block h4 {
			margin: 1rem 0rem 1rem 0.5rem;
		}
	}
	
	@media screen and (max-width: 480px) {
		.block img {
			width: 36px;
		}
	}

</style>

<fieldset id="_passwall2_status_fieldset" class="cbi-section">
	<div class="pure-g status">
		<div class="pure-u-1-4">
			<div class="block pure-g">
				<div class="pure-u-1-3">
					<div class="img-con">
						<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAATEAAAExCAYAAAAUZZVoAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAADIAAAAyABj+uetAAAikklEQVR42u3de5gddZ3n8XeSTggEcidcZAZZLFZXShwV2V0ccBSFclwvICpyWW+4OnlW5wFl5DKjQmYVkHUBFxEFBYRhNg94G6ZACCAQkNvDpRJCqFwMIZ10Ounu9DV93z+qOlRO+pw+p05VfX9V9X09j4/Q6T71PSfhk9+p8/39vtNQqgbL9aYDs4ADgfnAQmAJcHD4v8OARcDi8H8HAfOAOeHPzQLGgTFgFOgF+oAeYCfQAbQD28P/bwv/uSP89W5gABj2HVv65VAGmiZdgJJnud5MgnB6E3A08A7gncAJBEFlCg94BngBeAXYSBB6fb5jj0kXp2RoiJVIuKpaQBBUfwWcDHyUYPWUd88DDwBPAquALb5jD0gXpdKnIVZQlusBzAWOAU4EPkkQWmXSC9xJEG7PEgTbsHRRKlkaYgURhtYighXWx4DzgdnSdRnoKeB24BFgne/Yg9IFqeZoiOWY5XpzCO5ffQK4EJghXVMOrQR+AawAXtN7a/mjIZYj4WrrcOBDwN8B75WuqYB+AvwKeF7vqeWDhpjhLNebBhwFnAH8E0Grg8rGvwM/Blb6jt0tXYyanIaYgcIV15HAp4GrpOtRADwIXAM85jt2n3Qx6g0aYgaxXG8hwU35qwkaR5WZ7gSuA571HXtUupiy0xATZrleC/CfgcuAU6XrUQ27BLjNd+wt0oWUlYaYEMv1lgDnEay6VP49SfAX0aO+Y49IF1MmGmIZCu91vRP4LvBx6XpUar4O3OE7dod0IWWgIZaB8C3jacDNBJunVTncCFztO/YG6UKKTEMsRZbrHQCcBfxcuhYl6gHgIuAFPYkjeRpiKbBc7yCCbT/XSNeijLKK4M/FnzTMkqMhlqAwvL6K9nap2tYTfKjzhIZZ8zTEEmC53v7Alwl6h5Sq12rg875jPytdSJ5piDUhvGH/WYJTEZSK62Hga75jr5UuJI80xGIIWyVOIbhhq1RSbgEu9h17u3QheaIh1iDL9d4G3Aa8R7oWVVjfAq7Xs87qoyFWJ8v15gGXEzQyKpUFB7hPb/7XpiE2hfAonDOBf5WuRZXSo8B5vmNvki7EVBpiNViuZwH3AMdK16JK71vAtTojYF8aYpMIR5hdAPxAuhalIgaBE33Hfk66EJNoiFWwXO84ghMJ9peuRakqrgK+q8dnBzTEQuHq6xKCEyaUyoPjtVFWQwwAy/XeSjD1ZqF0LUo16ArgijLfKyt1iIWfPH6F4MgUpfJqO8G9snXShUgobYhZrrcYWA68X7oWpRJyHnB72frKShliluudCDwuXYdSKVgOfMl37B7pQrJSqhAL3z5ehLZOqOI71nfs1dJFZGG6dAFZsVxvPsHsQA0wVQarLNf7nHQRWSjFSsxyvbcTnKqpVNlcD1xY5E8vCx9ilut9kmDrkFJl9TJwsu/YO6QLSUNhQyy8/3UpQR+NUgps37EL946kkCFmud5s4A7gdOlalDLMR3zHdqWLSFLhQizs/3oK+A/StShlqKW+Y98gXURSChVilusdDZSya1mpBl0N/IPv2OPShTSrMCFmud67gdJvhlWqAXcDn/Mde0i6kGYUIsQs1/sgQQ+YUqoxTwMf8B27T7qQuHIfYtpCoVTTWgk6/DulC4kj1yFmud65BJOHlFLNGQT+Mo/j4nK77chyvfPRAFMqKfsBbZbrHS5dSKNyGWKW6/0dcJN0HUoV0BbL9Y6QLqIRuXs7GQbY/5WuQ6mCO8J37C3SRdQjVyEWvoXUFZhS2Tjcd+yt0kVMJTchpjfxlRJxsOkbx3MRYtpGoZSYLuDNvmPvki6kGuNv7IeNrBpgSsmYDzxjuZ6xc1iNDrFwK5F24islywJ+b7lei3QhkzE2xMLN3LoXUikzfBC4yXI96Tr2YWSIhcfp6GkUSpnlCwQHjRrFuBv74YGGq9HzwJQy1Tm+Y98hXcQEo1Zi4ZHSd6ABppTJfhXObjWCUSFGsFTVI6WVMt/jlusdJV0EGPR20nK9TwC/lq5DKVW3YWCh79i9kkUYsRIL50JqgCmVLzOB5eFtIDHiIRZO5i7cGCmlSuI04CLJAkQTNEzwB4EPSNahlGrah3zHFmlMl16JXYQGmFJF8IDlem+SuLDYSiz8iPZxqesrpRK3Hnir79gjWV5UZCUWduRrgClVLEcD38v6opmvxML7YCuAv8n62kqpTHzAd+yHs7qYxErsK2iAKVVkD1mutyiri2W6ErNc763AmiyvqZQScT9wmu/YqV8os5WY5XozgZVZXU8pJepU4JwsLpTl28lLgIUZXk8pJev2LMa/ZfJ20nK944AXsriWUsoozwHH+449ntYFUl+JhW8jn0z7OkopI70bODfNC2TxdvICwNghA0qp1N1qud5haT14qm8nLdezgFfTvIZSKhdS+7QytZVY2NSqo9aUUhB8WnlaGg+c5tvJTwPHpvj4Sql8cS3Xm5P0g6YSYpbrzQPuSv0lUUrlzcVJP2BaK7HLU34hlFLA3Jkz+MF/XMxVx2S2y6dZl1qu95YkHzDxG/uW670NeDmzl0SpEpo7cwafP3IRZy+exfg4fOLFNrbtHpYuq16PA3+d1E3+RFdi4XTg27J/TZQqh7kzZ7D06CX86f3H8Mk5Y4yOjdE2Y3aeAgzgfSR4GGrSbydPAd6T6cuhVAlMhNezH3wb/+Mv5rF1WxuzZs5k8cKF/Mhvky4vjhWW67Uk8UCJPAhAWNADYi+JUgU0d+YMzv3LRXzDWgLAru4eunt6mDVrFosXBVuRn9wpOjGtGWcDtzb7IEmuxD4r91ooVSzRldc3rCWMjY3Rtr2d7p4eZra0sGTxIqZNm8Ztm3YyOJbatsS0/TKJlotEVmKW6+0P3C79iiiVd5UrL4ChoSHa2ncAMGPGDA5ZcjDTpgWfyd35Wod0yc1aClzVzAMktRL7svQroVSeVa68JvT29u0JsGnTpnFoJMC6h0fZ0DcoXXqzrrRcr6kjuppeiVmudxBwnfQroVQeTbbyAhgbH6ejo5OB3bv3fO3wQw9h+vQ31h3LXtkqXX5SLgAui/vDSazEvir9CiiVN9VWXgDDwyNsad1aM8AAVrT1SD+NpFwaTkCLpamVWLgKa+r9rFJlUm3lNaG/f4CdnZ17fe2wQw9hxowZe33t/m3d9IyMSj+dJF1IzC1Jza7Ezpd+5krlQa2VF8D4+DidXV37BtghS2ipCDCAn21sl35KSft23HtjsVdilusdAFwj/cyVMtlUKy+AkdFR2ra3MzY2ttfXD12yhJaWyf8TXdU9IP3U0rAUuKLRH2pmJXaW9DNWylRTrbwm7N49yNZtbfsE2CFLDmbmzMkD7Mq128hva1hNl1uud2CjPxRrJRZ25/9c+hkrZZp6Vl4TJrrvKy05eDGzZs6s+nO/a+2SfpppOhv4aSM/EHcllsoJjUrlVb0rL2Cv7vtKSxYvYr9Zs6r+7Itd/bQPjkg/3TTdGA4XqlvDK7HwpIqbpZ+pUiZoZOUFe3ffVzp40UL222+/mj9/7brt0k85Cx8G7q33m+O8nXwnUN/vmFIF1Wh4QdB937lr16S/tnjhQmbPnj3lYzzd0Sf91LNwk+V6b6r3vLE4IfZd6WeolJQ44TVZ933UwgUL2H//qQPs5o07GCroHf0KhwM24NXzzQ2FmOV6S4CPSz9DpbIWJ7wg6L7ftr36W8AF8+cz54D6xrLetTn3m70bcTHwuXq+sdEb++dJPzOlstTIDftK/f0DNQNs/rx5HDjngLoeq31whE39Q9IvR5bOqrf5te6VWNhWcbX0M1MqC3FXXhB033ft2kVvX3/V75k39yAOOrD+o7S+X5zN3o04kzraLeoeFGK53vuAx6SflVJpaia8oHr3/V7XOOhA5s2d29DjvuvBl+kdGWvoZwpiuu/YNW8ENnJPLPZRGUqZrtnwgqD7vn3nzprfc+CcOQ0H2O9bu8oaYADHAS/U+oa6Qix8b3qq9LNRKmlJhBdU776PmnPAASyYP6/hx77lzzsb/pkC+TrwxVrfUO+N/Y9JPxOlktTMDfuoWt33UfvvP5uFC+bHusaankJu9q7XF8LDJqqaciUWdujrDX1VCEmtvKB2933U7P32Y/HCeCcwL1uztaibvRtxMuBW+8V63k4eCcQ+dVEpEyQZXlC7+z4qOlotjnu3Tn2NEriYJkPs09LPQKm4kg6vqbrvo6Kj1eJ4pqOPnUOF3uxdr7+2XG+B79idk/1izRCzXG8aevy0yqGkwwum7r6PqhytFsf160ux2btepwDLJ/uFqVZiR0lXrlQj0ggvmPzs+2oqR6vF9Vxnf1M/XzDfJmaInSFduVL1SCu86um+rzTZZKJG3bihnWG9ox/1Lsv15vuO3VX5C1VDLPxU8p+kK1eqlrTCC+rrvq+URIABLN9c36qvZE4Cflf5xVorscOBhs+7VioLaYYX1Nd9X2my0WpxvD4wxOaBUm32rtdSGgyxU6QrVqpS2uEF9XXfV6o2Wi2Oq9a2pfbccu7DluvN9h17r4+Ga4XYUumKlZqQRXiNjY3RvmMnQ8PDDf1crdFqcTzaXpjJ3mk4Dngq+oVJX3nL9eYA75WuVqkswgvq776vVGu0Whx3b+mkf7S0m73r8SkqQqzaHch3SFeqyi2pvY316O3tixVgU41Wi+PWcm/2rsc3ww8d96j2V8gnpCtV5ZTVygsa676vNNVotbjW9jReSwkdBuw5JXKfEAtT7pvSVapyyTK8oLHu+0r1jFaL4zurW9HOsLqcAPxm4l8mW4ktIv5QXaUaknV4QWPd95XqHa0Wx31tutm7Tp8lEmKThdVfSVeoii/Le14TxsfH6ezqih1g9Y5Wi2Pljl46h0YzeR0K4DOW6+3JrslWYnoAokqNxMoL4nXfRzUyWi2OG9a3Z/p6FMChQCtUhFh4P+x86epU8UiFF8Trvo9qZLRaXM936WbvBr2HsHu/ciU2F0hnvaxKSTK8IF73fVSjo9XiuG7ddkbG9ZZ+gz5KGGKV98SOka5MFYPEPa+oes++r/kcDjqQuQcdlHqt92zRzd4xfGGiX6xyJXaidGUq36RXXhC/+z4qzmi1ODb0DdI60Ng2JwUE2TUX6K4MsdOlK1P5ZEJ4Qf1n39cSd7RaHD/Uzd7NOAp4cU+IhR9ZniRdlcoXU8Krme77qGZGq8WxcmdvZtcqoOOJhhiwQLoilR+mhBc0130f1cxotTju2tzBgG72boYD/DwaYkdLV6TMZ1J4QXPd91HNjlaL4/ZNutm7SadbrrfXjX3t1FdVmRZecc6+r6bZ0WpxDI6Ns653MLPrFdgB0RB7v3Q1yjymhRc0330flcRotTguf1k3eyfksGiI/a10NcocJoYXNN99H5XUaLU4/tDWnfk1C8pqAbBcbyaQflefMp6p4QXNd99XSmoyUaMe2t7DrmHd7J2Qd0+sxLK9o6mMY3J4xT37vhapAAP46Qbd7J2gEydC7HDpSpQMk8MLkum+r5TUaLW4Xto1IHbtAjplIsTeIl2Jypbp4QXJdN9XSnK0WhzXvNrGqG72TtKeMS06GKQk8hBeSXXfV0p6tFocv23tEr1+EU38jr5TuhCVrjyEFyTXfV8p6dFqcazp2c223brZO2kTv6snSBei0pGX8ILkuu8rpTFaLY4fvaqbvdPQEm78Pli6EJWsPIVXkt33ldIarRbHk7rZOxUtgBm/wyoReQovSLb7vlJao9XiuG3TTgbH9IZ+GlrQJtdCyFt4QbLd95XSHK0Wx52vdUiXUFgtQDanv6lU5DG8IPnu+6g0R6vF0T08yoY+3eydlha0Wz+X8hpeaXTfR6U9Wi2OZa9slS6h0FqAfP1XUHJ5DS9Ip/s+KovRanGsaEtnxakCLegnk7mQ5/CCdLrvo7IYrRbH/du66RnRzd5p0pWY4fIeXml130dlNVotjp9t1M3eaWshGAeuDJP38IL0uu+jshqtFteqbt3snbYWYJF0EeoNRQgvSK/7PirL0WpxXLl2G9oalr4WYLF0Eao44ZVm931U1qPV4vidbvbOhK7EhBUlvCDd7vuorEerxfFiVz/tgyPSZZTCxChwlbEihRek230fJTFaLY5r16V7L1C9QTv2M1a08IJ0u++jJEarxfV0R590CaXRApjXHVhARQyvtLvvo6RGq8Vx88YdDOkd/cy0AGZs8y+oIoYXpN99HyU5Wi2OuzbrZu8s6VE8KSlqeEH63feVJCcTNap9cIRN/UPSZZRKC+gg4iQVObyy6L6vlKcAA/i+bvbOXAswBsiNfymIIocXZNN9X0l6tFocj7TrZu+stQCjgPwB5DlV9PCCbLrvK0mPVovj961d9I6k2yOn9tUC9ALmnCCXE2UIr6y67yuZMFotjlv+nH6fnNpXC9CHbj2qWxnCC7Lrvq9kwmi1uNb06GZvCS1At3QReVCW8ILsuu8rmTJaLY5la7bqZm8hLYA2tdRQpvCC7LrvK5k0Wi2Oe7dm13Ki9qYhVkXZwivL7vtKJo1Wi+OZjj52DulmbyktgB49GVG28IJsu+8rmTZaLY7r1+tmb0ktgP4OUM7wguy776NMG60W13Od2X56q/ZW+pVYWcNLovs+ysTRanHcuKGdYb2jL6oFaJMuQsr0afDwycdwUEu+miqbJdF9H2XqaLU4lm/OtglY7Ws6JX47OTYOJ6x4hf/9anlyvL9/QDTATB2tFsfrA0NsHtDN3tKmU/JPJ0fGx7lxQzt/+/i6Qs8HHB8fp7OrK/PtQ1Emj1aL46q15fnLz2TTAd0rAfi9uwu7KhsZHaV1W1vm24eiTB+tFsejutnbCNMB/Z0IFXFVtnv3IFu3tWW+fSjK9NFqcdy9pZP+Ud3sbYLpgH4+XKEoq7Jd3T0i24ei8jBaLY5bdbO3MaYD2bdo50CeV2VjY2O0bW8X2T4UlYfRanGt7ZFpTVH7mu47NoAnXYip8rYqGxoaYsvWbSLbh6LyMlotju+sbtXjkA0yce7v09KFmCwvq7Le3j6x7UNReRqtFsd9bbrZ2yQTIfaidCF5YOqqbGx8nB07O8S2D0XlabRaHCt39NI5ZO5fZGU0EWJrpQvJC9NWZcPDI2xp3Sq2fSgqb6PV4rhhfal36RlpIsQ2SBeSNyasyqS77yvlbTJRHM936Yf5hlk/8SfOrPdHOSG1KjOh+75SGQLsunXbGRnXW/qGeWjiT12fdCV5luWqzITu+0p5HK0Wxz1bzPlLQ+3x1HQA37HHgOelq8mzLFZlJnTfV8rjaLU4NvQN0jqgLZUGWhVd/z8gXU0RpLUqM6H7vlJeR6vF8UPd7G2qjdEQe1K6mqJIclVmSvd9pTyPVotj5c5e6RLU5DqiIbZKupqiaXZVZkr3faU8j1aL467NHQzoZm8Tveo79kg0xLZIV1REcVdlpnTfV8r7aLU4bt9k1tt4tcdv4Y0+MXzHHgB0zZySeldlJnXfV8r7aLU4BsfGWdc7KF2GmtyjEAmx0J3SVRXZVKsyk7rvKxVhtFocl7+sm70N9iLsG2L6CWUGJluVmdZ9H1WU0Wpx/KGtW7oEVd022DfEnpWuqiyiq7LtnWZ130cVZbRaHA9t72HXsPz+WDWp533HHoZ9Q0xv7mfM793Nxa+aeeO4SKPV4vjpBt3sbbA9t772CrEw2f4kXV3ZPNY5wMB0s3quijRaLa6Xdg1Il6Cqe2TiHybbsfsr6erK6MrXzGlmLdpotTiuebWNUd3sbbJXJv5hshB7RLq6MjJlNVbE0Wpx/La1S7oEVV2r79h72sEmC7F10hWWlfRqrIij1eJY07ObbbvN2iWh9nJT9F/2CTHfsQeBJ6SrLCPJ1VhRR6vF8SPDjh9X+/i36L9UO8XuFukqy0piNVbk0WpxPKmbvU33cvRfqoXYCukqyyrr1ViRR6vFcdumnQyO6Q19gz0TbpHco1qIvSZdaZlltRor+mi1OO58rUO6BFXbDZVfmDTEwpNeb5jy4VQqsliNFX20Whzdw6Ns6NPN3obb511irckOd0hXW2ZprsbKMFotjmWvbJUuQU3t9cov1AoxPXNfUJqrsTJMJopjRZs5DcdqUst8x97nhmXVP8nhzbN/l666zNJYjWmATe7+bd1GDENWNS2f7ItT/Wn+sXTVZZb0aqwso9Xi+NlG3eydA6sn++JUIbZSuuqyS2o1VpbRanGt6tbN3oa7znfsSZfKNUPMd+xu4EHp6sssidVYmUarxXHl2m1oa5jxflntF+q5OXKNdPVl18xqrGyj1eL4nW72zoOXqv1CPSH2mHT1ZdfMamzT5tfZ9LqedVnNi139tA+OSJeharuy2ltJqCPEfMfuQweIiGtmNdbZ1cWLq1/WMJvEtevMnGug9lJzL3e9n7VfJ/0syq7Ze2Pj4+MaZpN4uqNPugRV2wDwaq1vqDfEdICIAZL4pFLD7A03b9zBkN7RN93f+45d8xvqCrHw/egl0s+m7JLsG9Mwg7s262bvHLh7qm9opHX7Nulno5Lv4i9rmLUPjrCpf0i6DFWb6zv2lKPA6g4x37G3AE9KP6uyS2tPZdnC7Pu62TsPLq/nmxrdRHeZ9LNS6Z5wUZYwe6RdN3vnwNP1fFOjIfao9LNS2Zw3VuQw+31rF70jY9JlqNq+Fp5rOKWGQsx37BHg69LPTmV3+utEmP2XB1fzrZdeb/4BDXDLn82cuK728i/1fmOcM1n0sEQDZHkW//mt4+wcGee3rV28/Q/5D7M1PbrZ23A/9R17V73f3HCI+Y7dAdwo/SxVNqux81vH2RVZ1A+P5TvMlq3Zqpu9zXdVI98c93S8q6WfpUp/NVYZYFF5DbN7t9b9F7yS8UffsTc08gOxQiy8yAPSz1altxqrFWBReQqzZzr62Dmkm70N961Gf6CZc4ovkn62Kp3VWL0BFpWHMLt+vW72NtxG4JlGf6iZEHsB8KSftUp2NRYnwKJMDrPnOvulS1C1fXmqfZKTiR1i4cW+Iv2sVXKrsWYDLMq0MLtxQzvDekffZLuAh+P8YLNjb/4ErJd+9qr51ViSARZlSpgt39wpdm1Vl89MNo6tHk2FWLgaO0/62avmVmNpBViUZJi9PjDE5gHd7G2wHpr4oDCJAYRPUGWUkspWnNVYFgEWJRFmV61ty+4JqjhOr3eL0WSaDrFwNfZ56VdBNb4ayzrAorIMs0d1s7fJtgArmnmAREZB+479LDFvyqlk1bsakwywqLTD7O4tnfSPGvBEVTVnxr0XNiHJefZfE34xFPWtxkwJsKi0wuxW3extsid8x276jMLEQsx37LVMMZVEZaPWaszEAItKOszW9uyWfkqqui8l8SBJrsQALhZ4IVSFaqsx0wMsKokw+87qVrQzzFg/9x37lSQeKNEQ8x17OzH2PqnkVa7G8hRgUc2E2X1tutnbYIkNHkp6JQZwfYYvhKrisc4BhmcEq7G8BlhUo2G2ckcvnUOjdTyyEvA/fcduT+rBpqVRoeV6pwFuZi+JmtSHFs5m2+AoXt+wdCmJmzl9Gh85dB5Xv+OISX/97Kc28kynDsY11CzfsRP7Q5lWiAH8ETgpoxdFlVS1MPtP969mZFzviBnoZN+xE53VkUqIAViu92aCozWUSl00zK5bt50fr9Njdwx0j+/YZyT9oKmFGIDlet9ET4FVGZo5fRot06YxoA2uJjrUd+zE94ClcWM/6lpgMOVrKLXH8Ni4BpiZ/nsaAQYph1h48+7ENK+hlDLey8DtaT142isxfMd+Dn1LqVSZ/bdm90fWknqIhb6T0XWUUmZZ2uj0okZlEmK+Yw8Ax2dxLaWUMVaTwYzarFZiE8f1XJHV9ZRS4j7SzGGH9cosxEJXANrAo1TxneM79mtZXCjTENNPK5Uqhd8Ad2R1sVSbXauxXO9c4DaJayulUrfAd+yurC6W9dvJCbcDy4WurZRKz3uzDDAQWokBWK53IMGoJqVUMVzgO/aPsr6o1EoM37F7gWOlrq+UStT9wP+RuLDYSmyC5XqfI8ObgEqpVMz3HVvkKF2xldgE37HvRE+DVSrPjpUKMDAgxEIXEmwSVUrlyxm+Y6+WLMCIEAv7x06WrkMp1ZDv+Y59j3QRRoQYgO/YOwBbug6lVF3uBb4nXQQYcGO/kg4ZUcp424GjfMfuly4EDFqJTfAd+z5gqXQdSqmqjjMlwMDAEAPwHfsG9CBFpUz0dt+xt0kXEWVkiIX+ARC/aaiU2uMk37GN6yIw7p5YlOV6s4DHgPdK16JUyZ3uO/avpYuYjNEhBmC53hzgVeBw6VqUKqkv+o79C+kiqjH57SQAvmP3Eeyx1NFvSmXvApMDDHIQYgC+Y3cCR0rXoVTJ/KPEqRSNykWIAYSDN98kXYdSJfHPwDLpIuqRmxAD8B27FfgL6TqUKrj/BVzmO/nYQJOrEAPwHft14AjpOpQqqGXApXkJMMjBp5PVWK53GNAqXYdSBfKPwLI8BRjkOMQALNdbDPjAfOlalMo5kaOlk5C7t5NR4ckXbyYIMqVUPF/Ka4BBzkMMIDxR8jhghXQtSuXQJ33HvkW6iGbkPsQAfMceAE4DjG7KU8owJ/mO/RvpIppViBAD8B17BPgicJl0LUrlwNt9x35Muogk5PrGfjWW650N/Eq6DqUMtAOwTTtOpxmFWYlF+Y59B/A+6TqUMsy/AUcWKcCgoCEG4Dv2SuAoYFi6FqUMcDnwMZNOZE1KId9ORlmudyCwnODGv1JlZOxZYEkofIgBWK43DbgI+IF0LUpl7FjpuZBpK0WITbBc7xTgAek6lMrAA8CnfMfuli4kbYW9JzYZ37EfJNg8vl66FqVS9PfAqWUIMCjZSmyC5XotBIM/L5GuRamEneA79tPSRWSplCE2wXK9vwEekq5DqQT8Bvh8uA2vVEr1drKS79gPA4uB+6VrUaoJZxPsgSxdgEHJV2ITLNcDOAe4XboWpRqwBnB8x94kXYgkDbEIy/WOIFiWv1u6FqWmsBS40XfsMelCpGmIVQh7ys4FbpWuRalJrAI+7jv2BulCTKEhVkV4/PUvgFOla1Eq9AXgVt+xx6ULMYmGWA3hvbLTAFe6FlVq9wBf8x17u3QhJtIQq4PlenOAi4FLpWtRpXOy79iPShdhMg2xBliu9xaCt5h6zI9K2zeAn/iOraewTEFDrEHhW8wPoGf6q3TcDFzsO3a7dCF5oSEWU7h16Wzgl9K1qEJYCXzZd+xXpAvJGw2xJoX3y5YCV0rXonLpNeAs37GfkC4krzTEEmK53kLgAvTmv6pPN/Ap4EFtmWiOhljCwqnkFwLflq5FGakHOBN4QLvtk6EhlpJwZbaU4GxzpTYRNKs+oiuvZGmIpSw84/8c4CfStSgRfwS+CTzrO7Z0LYWkIZYRy/VmAh8GbgIOl65Hpe4nwA91j2P6NMQyFvaZ2QQ7AM6Srkcl7qvAv/qO3SVdSFloiAkK75udCdwoXYtqyr3APwNP6c367GmIGSA8/uc44OsEN3+V+foJBnLc4zv2TuliykxDzDCW6x0AnEwwxET3aJrn+wRnza3VG/Vm0BAzmOV6C4BTCHrO3iVdT4ldSxBcL/mOPSpdjNqbhlhOWK43HziJoPfsw9L1lMAy4P8Ba3zHHpEuRlWnIZZDluvNJriH9imCHiTVvGcI2iIeBF7XhtT80BDLubBl4zDgBOCzwGeka8qJVoJPhe8DVvmOPSBdkIpHQ6xgLNebDhwKvAf4KMGnnS3SdRngeeBfCIYlr/Udu1e6IJUMDbGCC1dqc4GjgOMJZgacIV1XytYBvwYeIZgOtFVPSC0uDbESCoPtAIK3oRbBqu2/EnwSOlO6vgasI9ib+CRBWG0EOvRGfLn8f+GUFkYW510jAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE2LTA5LTE3VDE1OjIxOjMyKzA4OjAwR74VGwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNS0wMi0yNFQxODo0NDo0NyswODowMM5zM3oAAABNdEVYdHNvZnR3YXJlAEltYWdlTWFnaWNrIDcuMC4xLTYgUTE2IHg4Nl82NCAyMDE2LTA5LTE3IGh0dHA6Ly93d3cuaW1hZ2VtYWdpY2sub3Jn3dmlTgAAAGN0RVh0c3ZnOmNvbW1lbnQAIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTcuMS4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgbjvP9wAAABh0RVh0VGh1bWI6OkRvY3VtZW50OjpQYWdlcwAxp/+7LwAAABh0RVh0VGh1bWI6OkltYWdlOjpIZWlnaHQAMzA1DH+L0QAAABd0RVh0VGh1bWI6OkltYWdlOjpXaWR0aAAzMDWfjtuMAAAAGXRFWHRUaHVtYjo6TWltZXR5cGUAaW1hZ2UvcG5nP7JWTgAAABd0RVh0VGh1bWI6Ok1UaW1lADE0MjQ3NzQ2ODfo5GxtAAAAEnRFWHRUaHVtYjo6U2l6ZQAxNS4yS0J7Sgm2AAAAX3RFWHRUaHVtYjo6VVJJAGZpbGU6Ly8vaG9tZS93d3dyb290L3NpdGUvd3d3LmVhc3lpY29uLm5ldC9jZG4taW1nLmVhc3lpY29uLmNuL3NyYy8xMTg1MS8xMTg1MTUzLnBuZx52BOUAAAAASUVORK5CYII=" />
					</div>
				</div>
				<div class="pure-u-2-3">
					<h4 id="status_node">Core<br /><span class="red">NOT RUNNING</span></h4>
				</div>
			</div>
		</div>
		<div class="pure-u-1-4 check" onclick="check_connect('baidu', 'https://www.baidu.com')">
			<div class="block pure-g">
				<div class="pure-u-1-3">
					<div class="img-con">
						<img src="data:image/png;base64,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" />
					</div>
				</div>
				<div class="pure-u-2-3">
					<h4 id="status_baidu">Baidu Connection<br /><span id="_baidu_status" class="red">Touch Check</span></h4>
				</div>
			</div>
		</div>
		<div class="pure-u-1-4 check" onclick="check_connect('google', 'https://www.google.com/generate_204')">
			<div class="block pure-g">
				<div class="pure-u-1-3">
					<div class="img-con">
						<img src="data:image/png;base64,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" alt=""/>
					</div>
				</div>
				<div class="pure-u-2-3">
					<h4 id="status_google">Google Connection<br /><span id="_google_status" class="red">Touch Check</span></h4>
				</div>
			</div>
		</div>
		<div class="pure-u-1-4 check" onclick="check_connect('github', 'https://github.com')">
			<div class="block pure-g">
				<div class="pure-u-1-3">
					<div class="img-con">
						<img src="data:image/png;base64,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" />
						</div>
				</div>
				<div class="pure-u-2-3">
					<h4 id="status_github">GitHub Connection<br /><span id="_github_status" class="red">Touch Check</span></h4>
				</div>
			</div>
		</div>
	  </div>
	<script>
		//<![CDATA[
		var imgs = document.getElementsByTagName('img');
		for (var i = 0 ; i < imgs.length; i++) {
			document.getElementsByTagName('img')[i].setAttribute("oncontextmenu","return false;");
			document.getElementsByTagName('img')[i].setAttribute("ondragstart","return false;");
		}
		XHR.poll(5, '/cgi-bin/luci/admin/services/passwall2/index_status', null,
			function (x, data) {
				if (data) {
					if (true) {
						var status_node = document.getElementById('status_node');
						if (status_node) {
							var text = 'Core<br />';
							if (data["global_status"])
								text += '<span class="green">RUNNING</span>';
							else
								text += '<span class="red">NOT RUNNING</span>';
							status_node.innerHTML = text;
						}
					}
				}
			});
			
		function check_connect(type, url) {
			var s = document.getElementById('_' + type + '_status');
			if (s) {
				var div = s.parentNode.parentNode.parentNode.parentNode;
				div.removeAttribute('onclick');
				s.innerHTML = 'Check...';
				var sendDate = (new Date()).getTime();
				XHR.get('/cgi-bin/luci/admin/services/passwall2/connect_status', {
						type: type,
						url : url
					},
					function(x, rv) {
						if (rv.ping_type && rv.ping_type == "curl") {
							var use_time = rv.use_time;
							if (use_time < 1000) {
								s.className="green";
							} else if (use_time < 2000) {
								s.className="yellow";
							} else {
								s.className="red";
							}
							s.innerHTML = use_time + " ms";
						} else if (rv.status) {
							s.className="green";
							s.innerHTML = "Working...";
						}
						else {
							s.className="red";
							s.innerHTML = 'Problem detected!';
						}
						div.setAttribute('onclick','check_connect("' + type + '","' + url + '")');
					}
				);
			}
			return false;
		}
//]]>
	</script>
</fieldset>
<div class="cbi-section" id="cbi-passwall2-global">
	
	
	
	

		<div class="cbi-section-node cbi-section-node-tabbed" id="cbi-passwall2-cfg013fd6">
			


	
	<div class="cbi-tabcontainer" id="container.passwall2.cfg013fd6.Main" data-tab="Main" data-tab-title="Main" data-tab-active="false">
		

		<div class="cbi-value" id="cbi-passwall2-cfg013fd6-enabled" data-index="1" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.enabled">Main switch</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Checkbox&#34;,&#34;0&#34;,{&#34;value_disabled&#34;:&#34;0&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.enabled&#34;,&#34;hiddenname&#34;:&#34;cbi.cbe.passwall2.cfg013fd6.enabled&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.enabled&#34;,&#34;value_enabled&#34;:&#34;1&#34;}]"></div>
		</div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-node" data-index="2" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.node"><a style='color: red'>Node</a></label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Select&#34;,&#34;nil&#34;,{&#34;y1CxHjaR&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 05]&#34;,&#34;OCOBxvTt&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 05]&#34;,&#34;xCfhuDx3&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 07]&#34;,&#34;AvQJ80wP&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 08]&#34;,&#34;gXIcIYbt&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 27]&#34;,&#34;274fuJny&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 04 [Premium]]&#34;,&#34;PVOX1mOp&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 23 [Premium]]&#34;,&#34;IoqCVkVb&#34;:&#34;Xray Trojanï¼[ð®ð³ India 01]&#34;,&#34;URLgYsIS&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 13]&#34;,&#34;GqPkCZ9m&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 16]&#34;,&#34;WkZJiNyq&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 02]&#34;,&#34;mrfBB429&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 21 [Premium]]&#34;,&#34;85nE58d9&#34;:&#34;Xray Trojanï¼[ð¦ðº Australia Sydney 02]&#34;,&#34;xN2vhBX7&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 20]&#34;,&#34;tgh0q4bA&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 09]&#34;,&#34;pwFpRopI&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 09]&#34;,&#34;ju6UcWgr&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK Coventry 03]&#34;,&#34;Rj6nUluk&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 19]&#34;,&#34;oNuXfW8k&#34;:&#34;Xray Trojanï¼[ð§ð¬ Bulgaria 01]&#34;,&#34;OronH9DG&#34;:&#34;Xray Trojanï¼[ð­ðº Hungary 01]&#34;,&#34;ySD5LNrz&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 08]&#34;,&#34;kSLDu2VA&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 05]&#34;,&#34;MUa8fZNt&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 04]&#34;,&#34;pyxZjgfd&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 02]&#34;,&#34;iHrTABnv&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 16]&#34;,&#34;R9PQqimD&#34;:&#34;Xray Trojanï¼[ð¸ðª Sweden 01]&#34;,&#34;hDeSbPq1&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 03]&#34;,&#34;xHr5Q8k3&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 11]&#34;,&#34;lyxDI1Zy&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 14]&#34;,&#34;L55nV8Xs&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 26]&#34;,&#34;DfWQyjmY&#34;:&#34;Xray Trojanï¼[ð¨ð­ Switzerland 01]&#34;,&#34;v6ZMCJaK&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 04]&#34;,&#34;icbULHWU&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 04]&#34;,&#34;O21C9ZrU&#34;:&#34;Xray Trojanï¼[ð¨ð¦ Canada 02]&#34;,&#34;0oGnn33I&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 06]&#34;,&#34;BdiH7fYd&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 03 [Premium]]&#34;,&#34;G7ub7HoU&#34;:&#34;Xray Trojanï¼[ð°ð· Korea 03 [Premium]]&#34;,&#34;4vAHb4Aj&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 17]&#34;,&#34;0tAe5mH6&#34;:&#34;Xray Trojanï¼[ð«ð· France 01]&#34;,&#34;PvOKFjuB&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 09]&#34;,&#34;3f4faA7S&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 11 [Premium]]&#34;,&#34;Mn0mlZJs&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 22 [Premium]]&#34;,&#34;QGU39y6D&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 13]&#34;,&#34;T6Q3rl5l&#34;:&#34;Xray Trojanï¼[ð³ð± Netherlands 01]&#34;,&#34;6oBGQMMJ&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 07]&#34;,&#34;5uMeBi4q&#34;:&#34;Xray Trojanï¼[ð¨ð± Chile 01]&#34;,&#34;0S8KDFHV&#34;:&#34;Xray Trojanï¼[ð·ðº Russia Moscow 01]&#34;,&#34;AxxvltmT&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 19]&#34;,&#34;uoqPLio7&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 09]&#34;,&#34;2XUDkiVT&#34;:&#34;Xray Trojanï¼[ð¨ð¦ Canada 01]&#34;,&#34;ufpjkw3j&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 01]&#34;,&#34;mlMgAaA3&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 23]&#34;,&#34;u8AybjA6&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 05 [Premium]]&#34;,&#34;q8xlLfBl&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 18]&#34;,&#34;xaIITPct&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 22 [Premium]]&#34;,&#34;iGHfbsJY&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 01]&#34;,&#34;qojexwBh&#34;:&#34;Xray Trojanï¼[ð°ð· Korea 04]&#34;,&#34;tgbHM3DM&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 03]&#34;,&#34;vjNkI7fG&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 05]&#34;,&#34;kKwdXm2h&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 08]&#34;,&#34;E1gwhbHu&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 11]&#34;,&#34;LtlRT6YZ&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 05]&#34;,&#34;SeZ7lvRZ&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 15]&#34;,&#34;DyQrMaE6&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 04]&#34;,&#34;4yNbvItS&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 18]&#34;,&#34;NHbySajH&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 01]&#34;,&#34;WvcmdHr6&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 10]&#34;,&#34;Q1yHdiJP&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 12]&#34;,&#34;NsX3eTna&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK Coventry 02]&#34;,&#34;isXZMzqi&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 04]&#34;,&#34;ajDKwax4&#34;:&#34;Xray Trojanï¼[ð°ð· Korea 01]&#34;,&#34;X6se8EHx&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 03]&#34;,&#34;WivtH2CA&#34;:&#34;Xray Trojanï¼[ð®ðª Ireland 02]&#34;,&#34;R6cGtUyR&#34;:&#34;Xray Trojanï¼[ð¦ðª United Arab Emirates 01]&#34;,&#34;DxIUtKcJ&#34;:&#34;Xray Trojanï¼[ð®ðª Ireland 01]&#34;,&#34;elvKZjEq&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 04]&#34;,&#34;ndxFujGb&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 03]&#34;,&#34;t9OgiBJI&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 03]&#34;,&#34;YR7LVR0F&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 14]&#34;,&#34;ZIfnV7Ir&#34;:&#34;Xray Trojanï¼[ð¨ð­ Switzerland 02]&#34;,&#34;XMJIbTwD&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 08]&#34;,&#34;nil&#34;:&#34;Close&#34;,&#34;xscW48Y8&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 10]&#34;,&#34;q4deZjXX&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 07]&#34;,&#34;ywUND7sh&#34;:&#34;Xray Trojanï¼[ð¹ð· Turkey 01]&#34;,&#34;P2H2sxjR&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 03]&#34;,&#34;mDuAXCGv&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 12]&#34;,&#34;o4yqJzhe&#34;:&#34;Xray Trojanï¼[ð°ð· Korea 02]&#34;,&#34;XbzXgEyy&#34;:&#34;Xray Trojanï¼[ð¦ð¹ Austria 01]&#34;,&#34;mohMBk7V&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 15]&#34;,&#34;Sr80HfKW&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 05]&#34;,&#34;7fV36HeQ&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 01]&#34;,&#34;A0Tkxxjt&#34;:&#34;Xray Trojanï¼[ð©ðª Germany 02]&#34;,&#34;she5FGj7&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 25]&#34;,&#34;uGk20Vxr&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 01 [Premium]]&#34;,&#34;r8Tg0xzx&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 08]&#34;,&#34;8ip9LMdt&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 02]&#34;,&#34;DeZXvdcv&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 05]&#34;,&#34;GEy9iXeL&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 09]&#34;,&#34;imcQdQHM&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 02]&#34;,&#34;MMy29Qhq&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 04]&#34;,&#34;B5kV9AlC&#34;:&#34;Xray Trojanï¼[ð¦ð· Argentina 01]&#34;,&#34;iB3FEy96&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 12]&#34;,&#34;RsvoKktQ&#34;:&#34;Xray Trojanï¼[ð¨ð¦ Canada 03]&#34;,&#34;JrdtxdYU&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 07]&#34;,&#34;DUz8Nhes&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 06 [Premium]]&#34;,&#34;WBO4uNjB&#34;:&#34;Xray Trojanï¼[ð©ðª Germany 01]&#34;,&#34;MdqUwSPu&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 08]&#34;,&#34;Xq1xY7XK&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 07]&#34;,&#34;mVqPRs4a&#34;:&#34;Xray Trojanï¼[ð§ð· Brazil 01]&#34;,&#34;Vz3brQy2&#34;:&#34;Xray Trojanï¼[ð®ð© Indonesia 01]&#34;,&#34;pM53JiQr&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 17]&#34;,&#34;U4b7dD9K&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 10]&#34;,&#34;KC011RZ4&#34;:&#34;Xray Trojanï¼[ð·ðº Russia St. Petersburg]&#34;,&#34;NNQerRrG&#34;:&#34;Xray Trojanï¼[ð®ð³ India 02]&#34;,&#34;MJiIQzJv&#34;:&#34;Xray Trojanï¼[ð¦ðº Australia Sydney 01 [Premium]]&#34;,&#34;9r2kF8Tq&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 02]&#34;,&#34;YUJ9Tmjd&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 21]&#34;,&#34;F4HkFR2F&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 10]&#34;,&#34;InQrt6XU&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 03]&#34;,&#34;64dIL3H5&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 06]&#34;,&#34;NWI1Fz07&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 02 [Premium]]&#34;,&#34;LpjlRTiE&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 06]&#34;,&#34;7eTB6gjC&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 01]&#34;,&#34;qUDgkyZa&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 06]&#34;,&#34;ylPGxA3d&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 06]&#34;,&#34;GQotgjuV&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 07]&#34;,&#34;phacD1ku&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 02]&#34;,&#34;ZY1jMwKx&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 02]&#34;,&#34;NOqDm7wg&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 20 [Premium]]&#34;,&#34;Exe7HkrM&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK Coventry 01]&#34;,&#34;MGQRphW2&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 01]&#34;,&#34;LcGtEEcP&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 01]&#34;,&#34;bfO7ej8e&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 06]&#34;,&#34;XDJHjrx2&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 07]&#34;,&#34;XCo7eE89&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 24]&#34;},{&#34;optional&#34;:false,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.node&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.node&#34;,&#34;sort&#34;:[&#34;nil&#34;,&#34;NHbySajH&#34;,&#34;8ip9LMdt&#34;,&#34;InQrt6XU&#34;,&#34;MMy29Qhq&#34;,&#34;kSLDu2VA&#34;,&#34;LpjlRTiE&#34;,&#34;Xq1xY7XK&#34;,&#34;kKwdXm2h&#34;,&#34;pwFpRopI&#34;,&#34;F4HkFR2F&#34;,&#34;xHr5Q8k3&#34;,&#34;mDuAXCGv&#34;,&#34;URLgYsIS&#34;,&#34;YR7LVR0F&#34;,&#34;mohMBk7V&#34;,&#34;iHrTABnv&#34;,&#34;pM53JiQr&#34;,&#34;q8xlLfBl&#34;,&#34;AxxvltmT&#34;,&#34;NOqDm7wg&#34;,&#34;mrfBB429&#34;,&#34;Mn0mlZJs&#34;,&#34;mlMgAaA3&#34;,&#34;ufpjkw3j&#34;,&#34;9r2kF8Tq&#34;,&#34;t9OgiBJI&#34;,&#34;elvKZjEq&#34;,&#34;y1CxHjaR&#34;,&#34;0oGnn33I&#34;,&#34;XDJHjrx2&#34;,&#34;MdqUwSPu&#34;,&#34;GEy9iXeL&#34;,&#34;uGk20Vxr&#34;,&#34;NWI1Fz07&#34;,&#34;BdiH7fYd&#34;,&#34;274fuJny&#34;,&#34;u8AybjA6&#34;,&#34;DUz8Nhes&#34;,&#34;GQotgjuV&#34;,&#34;ySD5LNrz&#34;,&#34;7fV36HeQ&#34;,&#34;pyxZjgfd&#34;,&#34;X6se8EHx&#34;,&#34;MUa8fZNt&#34;,&#34;Sr80HfKW&#34;,&#34;qUDgkyZa&#34;,&#34;JrdtxdYU&#34;,&#34;MGQRphW2&#34;,&#34;WkZJiNyq&#34;,&#34;hDeSbPq1&#34;,&#34;icbULHWU&#34;,&#34;vjNkI7fG&#34;,&#34;64dIL3H5&#34;,&#34;6oBGQMMJ&#34;,&#34;XMJIbTwD&#34;,&#34;PvOKFjuB&#34;,&#34;WvcmdHr6&#34;,&#34;E1gwhbHu&#34;,&#34;iB3FEy96&#34;,&#34;QGU39y6D&#34;,&#34;lyxDI1Zy&#34;,&#34;SeZ7lvRZ&#34;,&#34;GqPkCZ9m&#34;,&#34;4vAHb4Aj&#34;,&#34;4yNbvItS&#34;,&#34;Rj6nUluk&#34;,&#34;xN2vhBX7&#34;,&#34;YUJ9Tmjd&#34;,&#34;xaIITPct&#34;,&#34;PVOX1mOp&#34;,&#34;XCo7eE89&#34;,&#34;she5FGj7&#34;,&#34;L55nV8Xs&#34;,&#34;gXIcIYbt&#34;,&#34;T6Q3rl5l&#34;,&#34;KC011RZ4&#34;,&#34;0S8KDFHV&#34;,&#34;WBO4uNjB&#34;,&#34;A0Tkxxjt&#34;,&#34;0tAe5mH6&#34;,&#34;DfWQyjmY&#34;,&#34;ZIfnV7Ir&#34;,&#34;LcGtEEcP&#34;,&#34;ZY1jMwKx&#34;,&#34;ndxFujGb&#34;,&#34;v6ZMCJaK&#34;,&#34;LtlRT6YZ&#34;,&#34;Exe7HkrM&#34;,&#34;NsX3eTna&#34;,&#34;ju6UcWgr&#34;,&#34;R9PQqimD&#34;,&#34;oNuXfW8k&#34;,&#34;XbzXgEyy&#34;,&#34;DxIUtKcJ&#34;,&#34;WivtH2CA&#34;,&#34;ywUND7sh&#34;,&#34;OronH9DG&#34;,&#34;ajDKwax4&#34;,&#34;o4yqJzhe&#34;,&#34;G7ub7HoU&#34;,&#34;qojexwBh&#34;,&#34;7eTB6gjC&#34;,&#34;phacD1ku&#34;,&#34;tgbHM3DM&#34;,&#34;DyQrMaE6&#34;,&#34;OCOBxvTt&#34;,&#34;bfO7ej8e&#34;,&#34;xCfhuDx3&#34;,&#34;r8Tg0xzx&#34;,&#34;uoqPLio7&#34;,&#34;U4b7dD9K&#34;,&#34;2XUDkiVT&#34;,&#34;O21C9ZrU&#34;,&#34;RsvoKktQ&#34;,&#34;MJiIQzJv&#34;,&#34;85nE58d9&#34;,&#34;R6cGtUyR&#34;,&#34;IoqCVkVb&#34;,&#34;NNQerRrG&#34;,&#34;Vz3brQy2&#34;,&#34;mVqPRs4a&#34;,&#34;B5kV9AlC&#34;,&#34;5uMeBi4q&#34;,&#34;iGHfbsJY&#34;,&#34;imcQdQHM&#34;,&#34;P2H2sxjR&#34;,&#34;isXZMzqi&#34;,&#34;DeZXvdcv&#34;,&#34;ylPGxA3d&#34;,&#34;q4deZjXX&#34;,&#34;AvQJ80wP&#34;,&#34;tgh0q4bA&#34;,&#34;xscW48Y8&#34;,&#34;3f4faA7S&#34;,&#34;Q1yHdiJP&#34;],&#34;widget&#34;:&#34;select&#34;,&#34;size&#34;:1}]"></div>
		</div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-localhost_proxy" data-index="3" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.localhost_proxy">Localhost Proxy</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Checkbox&#34;,&#34;1&#34;,{&#34;value_disabled&#34;:&#34;0&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.localhost_proxy&#34;,&#34;hiddenname&#34;:&#34;cbi.cbe.passwall2.cfg013fd6.localhost_proxy&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.localhost_proxy&#34;,&#34;value_enabled&#34;:&#34;1&#34;}]"></div>
		
			<div class="cbi-value-description">
				When selected, localhost can transparent proxy.
			</div></div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-client_proxy" data-index="4" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.client_proxy">Client Proxy</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Checkbox&#34;,&#34;1&#34;,{&#34;value_disabled&#34;:&#34;0&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.client_proxy&#34;,&#34;hiddenname&#34;:&#34;cbi.cbe.passwall2.cfg013fd6.client_proxy&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.client_proxy&#34;,&#34;value_enabled&#34;:&#34;1&#34;}]"></div>
		
			<div class="cbi-value-description">
				When selected, devices in LAN can transparent proxy. Otherwise, it will not be proxy. But you can still use access control to allow the designated device to proxy.
			</div></div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-node_socks_port" data-index="5" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.node_socks_port">Node Socks Listen Port</label>
		<div class="cbi-value-field">



	<div data-ui-widget="[&#34;Textfield&#34;,&#34;1070&#34;,{&#34;optional&#34;:true,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.node_socks_port&#34;,&#34;datatype&#34;:&#34;port&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.node_socks_port&#34;}]"></div>


		</div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-node_socks_bind_local" data-index="6" data-depends="[{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;nil&#34;,&#34;!reverse&#34;:true}]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.node_socks_bind_local">Node Socks Bind Local</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Checkbox&#34;,&#34;1&#34;,{&#34;value_disabled&#34;:&#34;0&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.node_socks_bind_local&#34;,&#34;hiddenname&#34;:&#34;cbi.cbe.passwall2.cfg013fd6.node_socks_bind_local&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.node_socks_bind_local&#34;,&#34;value_enabled&#34;:&#34;1&#34;}]"></div>
		
			<div class="cbi-value-description">
				When selected, it can only be accessed localhost.
			</div></div></div>

<div class="cbi-value cbi-value-last" id="cbi-passwall2-cfg013fd6-socks_enabled" data-index="7" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.socks_enabled">Socks Main switch</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Checkbox&#34;,&#34;1&#34;,{&#34;value_disabled&#34;:&#34;0&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.socks_enabled&#34;,&#34;hiddenname&#34;:&#34;cbi.cbe.passwall2.cfg013fd6.socks_enabled&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.socks_enabled&#34;,&#34;value_enabled&#34;:&#34;1&#34;}]"></div>
		</div></div>


	</div>

	<div class="cbi-tabcontainer" id="container.passwall2.cfg013fd6.DNS" data-tab="DNS" data-tab-title="DNS" data-tab-active="false">
		

		<div class="cbi-value" id="cbi-passwall2-cfg013fd6-remote_dns_protocol" data-index="1" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.remote_dns_protocol">Remote DNS Protocol</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Select&#34;,&#34;tcp&#34;,{&#34;doh&#34;:&#34;DoH&#34;,&#34;tcp&#34;:&#34;TCP&#34;,&#34;udp&#34;:&#34;UDP&#34;},{&#34;optional&#34;:false,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;,&#34;sort&#34;:[&#34;tcp&#34;,&#34;doh&#34;,&#34;udp&#34;],&#34;widget&#34;:&#34;select&#34;,&#34;size&#34;:1}]"></div>
		</div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-remote_dns" data-index="2" data-depends="[{&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;udp&#34;}]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.remote_dns">Remote DNS</label>
		<div class="cbi-value-field">



	<div data-ui-widget="[&#34;Combobox&#34;,&#34;*******&#34;,{&#34;*******&#34;:&#34;******* (CloudFlare)&#34;,&#34;*******&#34;:&#34;******* (Google)&#34;,&#34;*******&#34;:&#34;******* (Quad9-Recommended)&#34;,&#34;*******&#34;:&#34;******* (Google)&#34;,&#34;**************&#34;:&#34;************** (OpenDNS)&#34;,&#34;**************&#34;:&#34;************** (OpenDNS)&#34;,&#34;*******&#34;:&#34;******* (CloudFlare-Security)&#34;,&#34;***************&#34;:&#34;*************** (Quad9-Recommended)&#34;},{&#34;optional&#34;:true,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns&#34;,&#34;datatype&#34;:&#34;or(ipaddr,ipaddrport)&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns&#34;,&#34;sort&#34;:[&#34;*******&#34;,&#34;*******&#34;,&#34;*******&#34;,&#34;*******&#34;,&#34;*******&#34;,&#34;***************&#34;,&#34;**************&#34;,&#34;**************&#34;]}]"></div>


		</div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-remote_dns_doh" data-index="3" data-depends="[{&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;}]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.remote_dns_doh">Remote DNS DoH</label>
		<div class="cbi-value-field">



	<div data-ui-widget="[&#34;Combobox&#34;,&#34;https:\/\/*******\/dns-query&#34;,{&#34;https:\/\/**************\/dns-query&#34;:&#34;OpenDNS&#34;,&#34;https:\/\/*******\/dns-query&#34;:&#34;CloudFlare-Security&#34;,&#34;https:\/\/doh.libredns.gr\/ads,**************&#34;:&#34;LibreDNS (No Ads)&#34;,&#34;https:\/\/*******\/dns-query&#34;:&#34;Google 8844&#34;,&#34;https:\/\/*******\/dns-query&#34;:&#34;Quad9-Recommended *******&#34;,&#34;https:\/\/doh.libredns.gr\/dns-query,**************&#34;:&#34;LibreDNS&#34;,&#34;https:\/\/***************\/dns-query&#34;:&#34;Quad9-Recommended ***************&#34;,&#34;https:\/\/dns.adguard.com\/dns-query,***************&#34;:&#34;AdGuard&#34;,&#34;https:\/\/*******\/dns-query&#34;:&#34;Google 8888&#34;,&#34;https:\/\/*******\/dns-query&#34;:&#34;CloudFlare&#34;},{&#34;optional&#34;:true,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_doh&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_doh&#34;,&#34;sort&#34;:[&#34;https:\/\/*******\/dns-query&#34;,&#34;https:\/\/*******\/dns-query&#34;,&#34;https:\/\/*******\/dns-query&#34;,&#34;https:\/\/*******\/dns-query&#34;,&#34;https:\/\/*******\/dns-query&#34;,&#34;https:\/\/***************\/dns-query&#34;,&#34;https:\/\/**************\/dns-query&#34;,&#34;https:\/\/dns.adguard.com\/dns-query,***************&#34;,&#34;https:\/\/doh.libredns.gr\/dns-query,**************&#34;,&#34;https:\/\/doh.libredns.gr\/ads,**************&#34;]}]"></div>


		</div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-remote_dns_client_ip" data-index="4" data-depends="[{&#34;cbid.passwall2.cfg013fd6.__hide&#34;:true},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NHbySajH&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NHbySajH&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;8ip9LMdt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;8ip9LMdt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;InQrt6XU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;InQrt6XU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MMy29Qhq&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MMy29Qhq&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;kSLDu2VA&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;kSLDu2VA&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;LpjlRTiE&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;LpjlRTiE&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Xq1xY7XK&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Xq1xY7XK&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;kKwdXm2h&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;kKwdXm2h&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;pwFpRopI&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;pwFpRopI&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;F4HkFR2F&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;F4HkFR2F&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xHr5Q8k3&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xHr5Q8k3&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mDuAXCGv&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mDuAXCGv&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;URLgYsIS&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;URLgYsIS&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;YR7LVR0F&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;YR7LVR0F&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mohMBk7V&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mohMBk7V&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;iHrTABnv&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;iHrTABnv&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;pM53JiQr&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;pM53JiQr&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;q8xlLfBl&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;q8xlLfBl&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;AxxvltmT&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;AxxvltmT&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NOqDm7wg&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NOqDm7wg&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mrfBB429&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mrfBB429&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Mn0mlZJs&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Mn0mlZJs&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mlMgAaA3&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mlMgAaA3&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ufpjkw3j&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ufpjkw3j&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;9r2kF8Tq&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;9r2kF8Tq&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;t9OgiBJI&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;t9OgiBJI&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;elvKZjEq&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;elvKZjEq&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;y1CxHjaR&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;y1CxHjaR&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;0oGnn33I&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;0oGnn33I&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XDJHjrx2&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XDJHjrx2&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MdqUwSPu&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MdqUwSPu&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;GEy9iXeL&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;GEy9iXeL&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;uGk20Vxr&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;uGk20Vxr&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NWI1Fz07&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NWI1Fz07&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;BdiH7fYd&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;BdiH7fYd&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;274fuJny&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;274fuJny&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;u8AybjA6&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;u8AybjA6&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DUz8Nhes&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DUz8Nhes&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;GQotgjuV&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;GQotgjuV&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ySD5LNrz&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ySD5LNrz&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;7fV36HeQ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;7fV36HeQ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;pyxZjgfd&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;pyxZjgfd&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;X6se8EHx&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;X6se8EHx&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MUa8fZNt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MUa8fZNt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Sr80HfKW&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Sr80HfKW&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;qUDgkyZa&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;qUDgkyZa&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;JrdtxdYU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;JrdtxdYU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MGQRphW2&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MGQRphW2&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WkZJiNyq&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WkZJiNyq&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;hDeSbPq1&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;hDeSbPq1&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;icbULHWU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;icbULHWU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;vjNkI7fG&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;vjNkI7fG&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;64dIL3H5&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;64dIL3H5&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;6oBGQMMJ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;6oBGQMMJ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XMJIbTwD&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XMJIbTwD&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;PvOKFjuB&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;PvOKFjuB&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WvcmdHr6&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WvcmdHr6&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;E1gwhbHu&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;E1gwhbHu&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;iB3FEy96&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;iB3FEy96&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;QGU39y6D&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;QGU39y6D&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;lyxDI1Zy&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;lyxDI1Zy&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;SeZ7lvRZ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;SeZ7lvRZ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;GqPkCZ9m&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;GqPkCZ9m&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;4vAHb4Aj&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;4vAHb4Aj&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;4yNbvItS&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;4yNbvItS&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Rj6nUluk&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Rj6nUluk&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xN2vhBX7&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xN2vhBX7&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;YUJ9Tmjd&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;YUJ9Tmjd&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xaIITPct&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xaIITPct&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;PVOX1mOp&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;PVOX1mOp&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XCo7eE89&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XCo7eE89&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;she5FGj7&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;she5FGj7&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;L55nV8Xs&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;L55nV8Xs&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;gXIcIYbt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;gXIcIYbt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;T6Q3rl5l&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;T6Q3rl5l&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;KC011RZ4&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;KC011RZ4&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;0S8KDFHV&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;0S8KDFHV&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WBO4uNjB&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WBO4uNjB&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;A0Tkxxjt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;A0Tkxxjt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;0tAe5mH6&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;0tAe5mH6&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DfWQyjmY&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DfWQyjmY&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ZIfnV7Ir&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ZIfnV7Ir&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;LcGtEEcP&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;LcGtEEcP&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ZY1jMwKx&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ZY1jMwKx&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ndxFujGb&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ndxFujGb&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;v6ZMCJaK&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;v6ZMCJaK&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;LtlRT6YZ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;LtlRT6YZ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Exe7HkrM&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Exe7HkrM&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NsX3eTna&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NsX3eTna&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ju6UcWgr&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ju6UcWgr&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;R9PQqimD&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;R9PQqimD&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;oNuXfW8k&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;oNuXfW8k&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XbzXgEyy&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XbzXgEyy&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DxIUtKcJ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DxIUtKcJ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WivtH2CA&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WivtH2CA&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ywUND7sh&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ywUND7sh&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;OronH9DG&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;OronH9DG&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ajDKwax4&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ajDKwax4&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;o4yqJzhe&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;o4yqJzhe&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;G7ub7HoU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;G7ub7HoU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;qojexwBh&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;qojexwBh&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;7eTB6gjC&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;7eTB6gjC&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;phacD1ku&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;phacD1ku&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;tgbHM3DM&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;tgbHM3DM&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DyQrMaE6&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DyQrMaE6&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;OCOBxvTt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;OCOBxvTt&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;bfO7ej8e&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;bfO7ej8e&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xCfhuDx3&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xCfhuDx3&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;r8Tg0xzx&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;r8Tg0xzx&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;uoqPLio7&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;uoqPLio7&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;U4b7dD9K&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;U4b7dD9K&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;2XUDkiVT&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;2XUDkiVT&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;O21C9ZrU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;O21C9ZrU&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;RsvoKktQ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;RsvoKktQ&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MJiIQzJv&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MJiIQzJv&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;85nE58d9&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;85nE58d9&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;R6cGtUyR&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;R6cGtUyR&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;IoqCVkVb&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;IoqCVkVb&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NNQerRrG&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NNQerRrG&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Vz3brQy2&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Vz3brQy2&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mVqPRs4a&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mVqPRs4a&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;B5kV9AlC&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;B5kV9AlC&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;5uMeBi4q&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;5uMeBi4q&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;iGHfbsJY&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;iGHfbsJY&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;imcQdQHM&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;imcQdQHM&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;P2H2sxjR&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;P2H2sxjR&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;isXZMzqi&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;isXZMzqi&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DeZXvdcv&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DeZXvdcv&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ylPGxA3d&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ylPGxA3d&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;q4deZjXX&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;q4deZjXX&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;AvQJ80wP&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;AvQJ80wP&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;tgh0q4bA&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;tgh0q4bA&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xscW48Y8&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xscW48Y8&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;3f4faA7S&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;3f4faA7S&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Q1yHdiJP&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;tcp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Q1yHdiJP&#34;,&#34;cbid.passwall2.cfg013fd6.remote_dns_protocol&#34;:&#34;doh&#34;}]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.remote_dns_client_ip">Remote DNS EDNS Client Subnet</label>
		<div class="cbi-value-field">



	<div data-ui-widget="[&#34;Textfield&#34;,null,{&#34;optional&#34;:true,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_client_ip&#34;,&#34;datatype&#34;:&#34;ipaddr&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_client_ip&#34;}]"></div>


		
			<div class="cbi-value-description">
				Notify the DNS server when the DNS query is notified, the location of the client (cannot be a private IP address).<br />This feature requires the DNS server to support the Edns Client Subnet (RFC7871).
			</div></div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-remote_dns_detour" data-index="5" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.remote_dns_detour">Remote DNS Outbound</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Select&#34;,&#34;remote&#34;,{&#34;remote&#34;:&#34;Remote&#34;,&#34;direct&#34;:&#34;Direct&#34;},{&#34;optional&#34;:false,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_detour&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_detour&#34;,&#34;sort&#34;:[&#34;remote&#34;,&#34;direct&#34;],&#34;widget&#34;:&#34;select&#34;,&#34;size&#34;:1}]"></div>
		</div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-remote_fakedns" data-index="6" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.remote_fakedns">FakeDNS</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Checkbox&#34;,&#34;0&#34;,{&#34;value_disabled&#34;:&#34;0&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.remote_fakedns&#34;,&#34;hiddenname&#34;:&#34;cbi.cbe.passwall2.cfg013fd6.remote_fakedns&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.remote_fakedns&#34;,&#34;value_enabled&#34;:&#34;1&#34;}]"></div>
		
			<div class="cbi-value-description">
				Use FakeDNS work in the shunt domain that proxy.
			</div></div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-remote_dns_query_strategy" data-index="7" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.remote_dns_query_strategy">Remote Query Strategy</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Select&#34;,&#34;UseIPv4&#34;,{&#34;UseIPv6&#34;:&#34;UseIPv6&#34;,&#34;UseIP&#34;:&#34;UseIP&#34;,&#34;UseIPv4&#34;:&#34;UseIPv4&#34;},{&#34;optional&#34;:false,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_query_strategy&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.remote_dns_query_strategy&#34;,&#34;sort&#34;:[&#34;UseIP&#34;,&#34;UseIPv4&#34;,&#34;UseIPv6&#34;],&#34;widget&#34;:&#34;select&#34;,&#34;size&#34;:1}]"></div>
		</div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-dns_hosts" data-index="8" data-depends="[{&#34;cbid.passwall2.cfg013fd6.__hide&#34;:true},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NHbySajH&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;8ip9LMdt&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;InQrt6XU&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MMy29Qhq&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;kSLDu2VA&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;LpjlRTiE&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Xq1xY7XK&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;kKwdXm2h&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;pwFpRopI&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;F4HkFR2F&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xHr5Q8k3&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mDuAXCGv&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;URLgYsIS&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;YR7LVR0F&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mohMBk7V&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;iHrTABnv&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;pM53JiQr&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;q8xlLfBl&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;AxxvltmT&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NOqDm7wg&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mrfBB429&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Mn0mlZJs&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mlMgAaA3&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ufpjkw3j&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;9r2kF8Tq&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;t9OgiBJI&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;elvKZjEq&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;y1CxHjaR&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;0oGnn33I&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XDJHjrx2&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MdqUwSPu&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;GEy9iXeL&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;uGk20Vxr&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NWI1Fz07&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;BdiH7fYd&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;274fuJny&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;u8AybjA6&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DUz8Nhes&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;GQotgjuV&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ySD5LNrz&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;7fV36HeQ&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;pyxZjgfd&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;X6se8EHx&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MUa8fZNt&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Sr80HfKW&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;qUDgkyZa&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;JrdtxdYU&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MGQRphW2&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WkZJiNyq&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;hDeSbPq1&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;icbULHWU&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;vjNkI7fG&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;64dIL3H5&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;6oBGQMMJ&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XMJIbTwD&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;PvOKFjuB&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WvcmdHr6&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;E1gwhbHu&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;iB3FEy96&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;QGU39y6D&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;lyxDI1Zy&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;SeZ7lvRZ&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;GqPkCZ9m&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;4vAHb4Aj&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;4yNbvItS&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Rj6nUluk&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xN2vhBX7&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;YUJ9Tmjd&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xaIITPct&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;PVOX1mOp&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XCo7eE89&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;she5FGj7&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;L55nV8Xs&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;gXIcIYbt&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;T6Q3rl5l&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;KC011RZ4&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;0S8KDFHV&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WBO4uNjB&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;A0Tkxxjt&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;0tAe5mH6&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DfWQyjmY&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ZIfnV7Ir&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;LcGtEEcP&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ZY1jMwKx&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ndxFujGb&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;v6ZMCJaK&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;LtlRT6YZ&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Exe7HkrM&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NsX3eTna&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ju6UcWgr&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;R9PQqimD&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;oNuXfW8k&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;XbzXgEyy&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DxIUtKcJ&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;WivtH2CA&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ywUND7sh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;OronH9DG&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ajDKwax4&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;o4yqJzhe&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;G7ub7HoU&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;qojexwBh&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;7eTB6gjC&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;phacD1ku&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;tgbHM3DM&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DyQrMaE6&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;OCOBxvTt&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;bfO7ej8e&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xCfhuDx3&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;r8Tg0xzx&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;uoqPLio7&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;U4b7dD9K&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;2XUDkiVT&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;O21C9ZrU&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;RsvoKktQ&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;MJiIQzJv&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;85nE58d9&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;R6cGtUyR&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;IoqCVkVb&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;NNQerRrG&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Vz3brQy2&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;mVqPRs4a&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;B5kV9AlC&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;5uMeBi4q&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;iGHfbsJY&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;imcQdQHM&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;P2H2sxjR&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;isXZMzqi&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;DeZXvdcv&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;ylPGxA3d&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;q4deZjXX&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;AvQJ80wP&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;tgh0q4bA&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;xscW48Y8&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;3f4faA7S&#34;},{&#34;cbid.passwall2.cfg013fd6.node&#34;:&#34;Q1yHdiJP&#34;}]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.dns_hosts">Domain Override</label>
		<div class="cbi-value-field">

	<textarea class="cbi-input-textarea"  style="width: 100%" data-update="change" name="cbid.passwall2.cfg013fd6.dns_hosts" id="cbid.passwall2.cfg013fd6.dns_hosts" rows="5" wrap="off">cloudflare-dns.com *******

dns.google.com *******</textarea>
		</div></div>

<div class="cbi-value" id="cbi-passwall2-cfg013fd6-write_ipset_direct" data-index="9" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.write_ipset_direct">Direct DNS result write to IPSet</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Checkbox&#34;,&#34;1&#34;,{&#34;value_disabled&#34;:&#34;0&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.write_ipset_direct&#34;,&#34;hiddenname&#34;:&#34;cbi.cbe.passwall2.cfg013fd6.write_ipset_direct&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.write_ipset_direct&#34;,&#34;value_enabled&#34;:&#34;1&#34;}]"></div>
		
			<div class="cbi-value-description">
				Perform the matching direct domain name rules into IP to IPSet/NFTSet, and then connect directly (not entering the core). Maybe conflict with some special circumstances.
			</div></div></div>

<div class="cbi-value cbi-value-last" id="cbi-passwall2-cfg013fd6-clear_ipset" data-index="10" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.clear_ipset">Clear IPSet</label>
		<div class="cbi-value-field">

	
		<input class="btn cbi-button cbi-button-remove" type="submit" name="cbid.passwall2.cfg013fd6.clear_ipset" id="cbid.passwall2.cfg013fd6.clear_ipset" value="Clear IPSet" />
	
		<br />
			<div class="cbi-value-description">
				Try this feature if the rule modification does not take effect.
			</div></div></div>


	</div>

	<div class="cbi-tabcontainer" id="container.passwall2.cfg013fd6.log" data-tab="log" data-tab-title="Log" data-tab-active="false">
		

		<div class="cbi-value" id="cbi-passwall2-cfg013fd6-log_node" data-index="1" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.log_node">Enable Node Log</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Checkbox&#34;,&#34;1&#34;,{&#34;value_disabled&#34;:&#34;0&#34;,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.log_node&#34;,&#34;hiddenname&#34;:&#34;cbi.cbe.passwall2.cfg013fd6.log_node&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.log_node&#34;,&#34;value_enabled&#34;:&#34;1&#34;}]"></div>
		</div></div>

<div class="cbi-value cbi-value-last" id="cbi-passwall2-cfg013fd6-loglevel" data-index="2" data-depends="[]"><label class="cbi-value-title" for="cbid.passwall2.cfg013fd6.loglevel">Log Level</label>
		<div class="cbi-value-field">

<div data-ui-widget="[&#34;Select&#34;,&#34;warning&#34;,{&#34;error&#34;:&#34;error&#34;,&#34;warning&#34;:&#34;warning&#34;,&#34;info&#34;:&#34;info&#34;,&#34;debug&#34;:&#34;debug&#34;},{&#34;optional&#34;:false,&#34;name&#34;:&#34;cbid.passwall2.cfg013fd6.loglevel&#34;,&#34;id&#34;:&#34;cbid.passwall2.cfg013fd6.loglevel&#34;,&#34;sort&#34;:[&#34;debug&#34;,&#34;info&#34;,&#34;warning&#34;,&#34;error&#34;],&#34;widget&#34;:&#34;select&#34;,&#34;size&#34;:1}]"></div>
		</div></div>


	</div>

	<div class="cbi-tabcontainer" id="container.passwall2.cfg013fd6.faq" data-tab="faq" data-tab-title="FAQ" data-tab-active="false">
		

		<style>
    .dns-con {
      padding: 1rem;
    }
    .faq-title {
      color: var(--primary);
      font-weight: bolder;
      margin-bottom: 0.5rem;
      display: inline-block;
    }
    .reset-title {
     color: var(--primary)ï¼
     font-weight: bolder;
     margin-bottom: 0.3rem;
     display: inline-block;
     margin-top: 1.2rem;
     text-decoration: underline;
    }
    .dns-item {
     margin-bottom: 0.8rem;
     line-height:1.2rem;
    }
    .dns-list {
		text-indent:1rem;
		line-height: 1.2rem;
}
</style>
<div class="cbi-section cbi-tblsection dns-con">
	<div id="faq_dns">
		<ul>
            <b class="faq-title">DNS related issues:</b>
			<li class="dns-item">1. <span>Certain browsers such as Chrome have built-in DNS service, which may affect DNS resolution settings. You can go to &#39;Settings -&#62; Privacy and security -&#62; Use secure DNS&#39; menu to turn it off.</span></li>
			<li class="dns-item">2. <span>If you are unable to access the internet after reboot, please try clearing the cache of your terminal devices (make sure to close all open browser application windows first, this step is especially important):</span>
			    <ul><li class="dns-list"> â¦ <span>For Windows systems, open Command Prompt and run the command &#39;ipconfig /flushdns&#39;.</span></li>
			        <li class="dns-list"> â¦ <span>For Mac systems, open Terminal and run the command &#39;sudo killall -HUP mDNSResponder&#39;.</span></li>
			        <li class="dns-list"> â¦ <span>For mobile devices, you can clear it by reconnecting to the network, such as toggling Airplane Mode and reconnecting to WiFi.</span></li>
			    </ul>
			</li>
			<li class="dns-item">3. <span>Please make sure your device&#39;s network settings point both the DNS server and default gateway to this router, to ensure DNS queries are properly routed.</span></li>
		</ul>
	</div>
	<div id="faq_reset"></div>
</div>

<script>
	var origin = window.location.origin;
	var reset_url = origin + "/cgi-bin/luci/admin/services/passwall2/reset_config";
	var hide_url = origin + "/cgi-bin/luci/admin/services/passwall2/hide";
	var show_url = origin + "/cgi-bin/luci/admin/services/passwall2/show";
	
	function reset(url) {
		if (confirm('Are you sure to reset?') == true) {
			window.location.href = reset_url;
		}
	}
	
	function hide(url) {
		if (confirm('Are you sure to hide?') == true) {
			window.location.href = hide_url;
		}
	}
	
	var dom = document.getElementById("faq_reset");
    if (dom) {
		var li = "";
		li += "<a href='#' class='reset-title' onclick='reset()'>" + " Restore to default configuration:"+ "</a>" + "<br />" + "  Browser access: " + "<a href='#' onclick='reset()'>" + reset_url + "</a>" + "<br />";
		li += "<a href='#' class='reset-title' onclick='hide()'>" + " Hide in main menu:"+ "</a>" + "<br />" + " Browser access: " + "<a href='#' onclick='hide()'>" + hide_url + "</a>" + "<br />";
		li += "<a href='#' class='reset-title'>" + " Show in main menu:"+ "</a>" + "<br />" +" Browser access: " + "<a href='#'>" + show_url + "</a>" + "<br />";
		dom.innerHTML = li;
	}
</script>

	</div>








		</div>

	

	
</div><!-- tblsection -->
<div class="cbi-section cbi-tblsection" id="cbi-passwall2-socks">
	<h3>Socks Config</h3><div class="cbi-section-descr"></div>
	<table class="table cbi-section-table"><tr class="tr cbi-section-table-titles anonymous"><th class="th cbi-section-table-cell" data-widget="dvalue">Status</th><th class="th cbi-section-table-cell" data-widget="fvalue">Enable</th><th class="th cbi-section-table-cell" data-widget="lvalue">Socks Node</th><th class="th cbi-section-table-cell" data-widget="value">Socks Listen Port</th><th class="th cbi-section-table-cell" data-widget="value">HTTP Listen Port 0 is not use</th><th class="th cbi-section-table-cell cbi-section-actions"></th></tr><tr class="tr cbi-section-table-row cbi-rowstyle-1" id="cbi-passwall2-3hhWmggO"><td class="td cbi-value-field" data-name="status" data-widget="dvalue" data-title="Status">
<div id="cbi-passwall2-3hhWmggO-status" data-index="" data-depends="[]">


<div class="_status" socks_id="3hhWmggO"></div>
<input type="hidden" id="cbid.passwall2.3hhWmggO.status" value="&#60;div class=&#34;_status&#34; socks_id=&#34;3hhWmggO&#34;&#62;&#60;/div&#62;" />
</div>
</td>


<td class="td cbi-value-field" data-name="enabled" data-widget="fvalue" data-title="Enable">
<div id="cbi-passwall2-3hhWmggO-enabled" data-index="" data-depends="[]">


<div data-ui-widget="[&#34;Checkbox&#34;,&#34;1&#34;,{&#34;value_disabled&#34;:&#34;0&#34;,&#34;name&#34;:&#34;cbid.passwall2.3hhWmggO.enabled&#34;,&#34;hiddenname&#34;:&#34;cbi.cbe.passwall2.3hhWmggO.enabled&#34;,&#34;id&#34;:&#34;cbid.passwall2.3hhWmggO.enabled&#34;,&#34;value_enabled&#34;:&#34;1&#34;}]"></div>
</div>
</td>


<td class="td cbi-value-field" data-name="node" data-widget="lvalue" data-title="Socks Node">
<div id="cbi-passwall2-3hhWmggO-node" data-index="" data-depends="[]">


<div data-ui-widget="[&#34;Select&#34;,&#34;NHbySajH&#34;,{&#34;y1CxHjaR&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 05]&#34;,&#34;OCOBxvTt&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 05]&#34;,&#34;xCfhuDx3&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 07]&#34;,&#34;AvQJ80wP&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 08]&#34;,&#34;gXIcIYbt&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 27]&#34;,&#34;274fuJny&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 04 [Premium]]&#34;,&#34;PVOX1mOp&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 23 [Premium]]&#34;,&#34;IoqCVkVb&#34;:&#34;Xray Trojanï¼[ð®ð³ India 01]&#34;,&#34;URLgYsIS&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 13]&#34;,&#34;GqPkCZ9m&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 16]&#34;,&#34;WkZJiNyq&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 02]&#34;,&#34;mrfBB429&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 21 [Premium]]&#34;,&#34;85nE58d9&#34;:&#34;Xray Trojanï¼[ð¦ðº Australia Sydney 02]&#34;,&#34;xN2vhBX7&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 20]&#34;,&#34;tgh0q4bA&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 09]&#34;,&#34;9r2kF8Tq&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 02]&#34;,&#34;ju6UcWgr&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK Coventry 03]&#34;,&#34;Rj6nUluk&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 19]&#34;,&#34;oNuXfW8k&#34;:&#34;Xray Trojanï¼[ð§ð¬ Bulgaria 01]&#34;,&#34;OronH9DG&#34;:&#34;Xray Trojanï¼[ð­ðº Hungary 01]&#34;,&#34;ySD5LNrz&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 08]&#34;,&#34;kSLDu2VA&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 05]&#34;,&#34;MUa8fZNt&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 04]&#34;,&#34;pyxZjgfd&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 02]&#34;,&#34;iHrTABnv&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 16]&#34;,&#34;R9PQqimD&#34;:&#34;Xray Trojanï¼[ð¸ðª Sweden 01]&#34;,&#34;hDeSbPq1&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 03]&#34;,&#34;xHr5Q8k3&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 11]&#34;,&#34;lyxDI1Zy&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 14]&#34;,&#34;L55nV8Xs&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 26]&#34;,&#34;ywUND7sh&#34;:&#34;Xray Trojanï¼[ð¹ð· Turkey 01]&#34;,&#34;v6ZMCJaK&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 04]&#34;,&#34;icbULHWU&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 04]&#34;,&#34;O21C9ZrU&#34;:&#34;Xray Trojanï¼[ð¨ð¦ Canada 02]&#34;,&#34;JrdtxdYU&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 07]&#34;,&#34;BdiH7fYd&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 03 [Premium]]&#34;,&#34;G7ub7HoU&#34;:&#34;Xray Trojanï¼[ð°ð· Korea 03 [Premium]]&#34;,&#34;4vAHb4Aj&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 17]&#34;,&#34;0tAe5mH6&#34;:&#34;Xray Trojanï¼[ð«ð· France 01]&#34;,&#34;PvOKFjuB&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 09]&#34;,&#34;3f4faA7S&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 11 [Premium]]&#34;,&#34;Mn0mlZJs&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 22 [Premium]]&#34;,&#34;QGU39y6D&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 13]&#34;,&#34;T6Q3rl5l&#34;:&#34;Xray Trojanï¼[ð³ð± Netherlands 01]&#34;,&#34;6oBGQMMJ&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 07]&#34;,&#34;5uMeBi4q&#34;:&#34;Xray Trojanï¼[ð¨ð± Chile 01]&#34;,&#34;0S8KDFHV&#34;:&#34;Xray Trojanï¼[ð·ðº Russia Moscow 01]&#34;,&#34;AxxvltmT&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 19]&#34;,&#34;uoqPLio7&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 09]&#34;,&#34;2XUDkiVT&#34;:&#34;Xray Trojanï¼[ð¨ð¦ Canada 01]&#34;,&#34;ufpjkw3j&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 01]&#34;,&#34;mlMgAaA3&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 23]&#34;,&#34;u8AybjA6&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 05 [Premium]]&#34;,&#34;r8Tg0xzx&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 08]&#34;,&#34;xaIITPct&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 22 [Premium]]&#34;,&#34;iGHfbsJY&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 01]&#34;,&#34;qojexwBh&#34;:&#34;Xray Trojanï¼[ð°ð· Korea 04]&#34;,&#34;tgbHM3DM&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 03]&#34;,&#34;vjNkI7fG&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 05]&#34;,&#34;kKwdXm2h&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 08]&#34;,&#34;E1gwhbHu&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 11]&#34;,&#34;LtlRT6YZ&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 05]&#34;,&#34;SeZ7lvRZ&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 15]&#34;,&#34;DyQrMaE6&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 04]&#34;,&#34;4yNbvItS&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 18]&#34;,&#34;NHbySajH&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 01]&#34;,&#34;WvcmdHr6&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 10]&#34;,&#34;Q1yHdiJP&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 12]&#34;,&#34;NsX3eTna&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK Coventry 02]&#34;,&#34;isXZMzqi&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 04]&#34;,&#34;ajDKwax4&#34;:&#34;Xray Trojanï¼[ð°ð· Korea 01]&#34;,&#34;X6se8EHx&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 03]&#34;,&#34;WivtH2CA&#34;:&#34;Xray Trojanï¼[ð®ðª Ireland 02]&#34;,&#34;R6cGtUyR&#34;:&#34;Xray Trojanï¼[ð¦ðª United Arab Emirates 01]&#34;,&#34;DxIUtKcJ&#34;:&#34;Xray Trojanï¼[ð®ðª Ireland 01]&#34;,&#34;elvKZjEq&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 04]&#34;,&#34;ndxFujGb&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 03]&#34;,&#34;t9OgiBJI&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 03]&#34;,&#34;YR7LVR0F&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 14]&#34;,&#34;ZIfnV7Ir&#34;:&#34;Xray Trojanï¼[ð¨ð­ Switzerland 02]&#34;,&#34;XMJIbTwD&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 08]&#34;,&#34;xscW48Y8&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 10]&#34;,&#34;q4deZjXX&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 07]&#34;,&#34;qUDgkyZa&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 06]&#34;,&#34;7fV36HeQ&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 01]&#34;,&#34;mDuAXCGv&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 12]&#34;,&#34;o4yqJzhe&#34;:&#34;Xray Trojanï¼[ð°ð· Korea 02]&#34;,&#34;XbzXgEyy&#34;:&#34;Xray Trojanï¼[ð¦ð¹ Austria 01]&#34;,&#34;mohMBk7V&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 15]&#34;,&#34;Sr80HfKW&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Los Angeles 05]&#34;,&#34;DfWQyjmY&#34;:&#34;Xray Trojanï¼[ð¨ð­ Switzerland 01]&#34;,&#34;A0Tkxxjt&#34;:&#34;Xray Trojanï¼[ð©ðª Germany 02]&#34;,&#34;she5FGj7&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 25]&#34;,&#34;uGk20Vxr&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 01 [Premium]]&#34;,&#34;P2H2sxjR&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 03]&#34;,&#34;8ip9LMdt&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 02]&#34;,&#34;DeZXvdcv&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 05]&#34;,&#34;GEy9iXeL&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 09]&#34;,&#34;imcQdQHM&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 02]&#34;,&#34;NWI1Fz07&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 02 [Premium]]&#34;,&#34;q8xlLfBl&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 18]&#34;,&#34;iB3FEy96&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 12]&#34;,&#34;RsvoKktQ&#34;:&#34;Xray Trojanï¼[ð¨ð¦ Canada 03]&#34;,&#34;MMy29Qhq&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 04]&#34;,&#34;DUz8Nhes&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 06 [Premium]]&#34;,&#34;WBO4uNjB&#34;:&#34;Xray Trojanï¼[ð©ðª Germany 01]&#34;,&#34;MdqUwSPu&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 08]&#34;,&#34;Xq1xY7XK&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 07]&#34;,&#34;B5kV9AlC&#34;:&#34;Xray Trojanï¼[ð¦ð· Argentina 01]&#34;,&#34;mVqPRs4a&#34;:&#34;Xray Trojanï¼[ð§ð· Brazil 01]&#34;,&#34;pM53JiQr&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 17]&#34;,&#34;U4b7dD9K&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 10]&#34;,&#34;KC011RZ4&#34;:&#34;Xray Trojanï¼[ð·ðº Russia St. Petersburg]&#34;,&#34;Vz3brQy2&#34;:&#34;Xray Trojanï¼[ð®ð© Indonesia 01]&#34;,&#34;NNQerRrG&#34;:&#34;Xray Trojanï¼[ð®ð³ India 02]&#34;,&#34;MJiIQzJv&#34;:&#34;Xray Trojanï¼[ð¦ðº Australia Sydney 01 [Premium]]&#34;,&#34;YUJ9Tmjd&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 21]&#34;,&#34;F4HkFR2F&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 10]&#34;,&#34;InQrt6XU&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 03]&#34;,&#34;pwFpRopI&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 09]&#34;,&#34;64dIL3H5&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 06]&#34;,&#34;0oGnn33I&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 06]&#34;,&#34;LpjlRTiE&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 06]&#34;,&#34;7eTB6gjC&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 01]&#34;,&#34;ylPGxA3d&#34;:&#34;Xray Trojanï¼[ð¸ð¬ Singapore 06]&#34;,&#34;GQotgjuV&#34;:&#34;Xray Trojanï¼[ðºð¸ USA San Jose 07]&#34;,&#34;phacD1ku&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 02]&#34;,&#34;NOqDm7wg&#34;:&#34;Xray Trojanï¼[ð­ð° Hong Kong 20 [Premium]]&#34;,&#34;ZY1jMwKx&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 02]&#34;,&#34;Exe7HkrM&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK Coventry 01]&#34;,&#34;MGQRphW2&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 01]&#34;,&#34;LcGtEEcP&#34;:&#34;Xray Trojanï¼[ð¬ð§ UK London 01]&#34;,&#34;bfO7ej8e&#34;:&#34;Xray Trojanï¼[ð¨ð³ Taiwan 06]&#34;,&#34;XDJHjrx2&#34;:&#34;Xray Trojanï¼[ðºð¸ USA Seattle 07]&#34;,&#34;XCo7eE89&#34;:&#34;Xray Trojanï¼[ð¯ðµ Japan 24]&#34;},{&#34;optional&#34;:false,&#34;name&#34;:&#34;cbid.passwall2.3hhWmggO.node&#34;,&#34;id&#34;:&#34;cbid.passwall2.3hhWmggO.node&#34;,&#34;sort&#34;:[&#34;NHbySajH&#34;,&#34;8ip9LMdt&#34;,&#34;InQrt6XU&#34;,&#34;MMy29Qhq&#34;,&#34;kSLDu2VA&#34;,&#34;LpjlRTiE&#34;,&#34;Xq1xY7XK&#34;,&#34;kKwdXm2h&#34;,&#34;pwFpRopI&#34;,&#34;F4HkFR2F&#34;,&#34;xHr5Q8k3&#34;,&#34;mDuAXCGv&#34;,&#34;URLgYsIS&#34;,&#34;YR7LVR0F&#34;,&#34;mohMBk7V&#34;,&#34;iHrTABnv&#34;,&#34;pM53JiQr&#34;,&#34;q8xlLfBl&#34;,&#34;AxxvltmT&#34;,&#34;NOqDm7wg&#34;,&#34;mrfBB429&#34;,&#34;Mn0mlZJs&#34;,&#34;mlMgAaA3&#34;,&#34;ufpjkw3j&#34;,&#34;9r2kF8Tq&#34;,&#34;t9OgiBJI&#34;,&#34;elvKZjEq&#34;,&#34;y1CxHjaR&#34;,&#34;0oGnn33I&#34;,&#34;XDJHjrx2&#34;,&#34;MdqUwSPu&#34;,&#34;GEy9iXeL&#34;,&#34;uGk20Vxr&#34;,&#34;NWI1Fz07&#34;,&#34;BdiH7fYd&#34;,&#34;274fuJny&#34;,&#34;u8AybjA6&#34;,&#34;DUz8Nhes&#34;,&#34;GQotgjuV&#34;,&#34;ySD5LNrz&#34;,&#34;7fV36HeQ&#34;,&#34;pyxZjgfd&#34;,&#34;X6se8EHx&#34;,&#34;MUa8fZNt&#34;,&#34;Sr80HfKW&#34;,&#34;qUDgkyZa&#34;,&#34;JrdtxdYU&#34;,&#34;MGQRphW2&#34;,&#34;WkZJiNyq&#34;,&#34;hDeSbPq1&#34;,&#34;icbULHWU&#34;,&#34;vjNkI7fG&#34;,&#34;64dIL3H5&#34;,&#34;6oBGQMMJ&#34;,&#34;XMJIbTwD&#34;,&#34;PvOKFjuB&#34;,&#34;WvcmdHr6&#34;,&#34;E1gwhbHu&#34;,&#34;iB3FEy96&#34;,&#34;QGU39y6D&#34;,&#34;lyxDI1Zy&#34;,&#34;SeZ7lvRZ&#34;,&#34;GqPkCZ9m&#34;,&#34;4vAHb4Aj&#34;,&#34;4yNbvItS&#34;,&#34;Rj6nUluk&#34;,&#34;xN2vhBX7&#34;,&#34;YUJ9Tmjd&#34;,&#34;xaIITPct&#34;,&#34;PVOX1mOp&#34;,&#34;XCo7eE89&#34;,&#34;she5FGj7&#34;,&#34;L55nV8Xs&#34;,&#34;gXIcIYbt&#34;,&#34;T6Q3rl5l&#34;,&#34;KC011RZ4&#34;,&#34;0S8KDFHV&#34;,&#34;WBO4uNjB&#34;,&#34;A0Tkxxjt&#34;,&#34;0tAe5mH6&#34;,&#34;DfWQyjmY&#34;,&#34;ZIfnV7Ir&#34;,&#34;LcGtEEcP&#34;,&#34;ZY1jMwKx&#34;,&#34;ndxFujGb&#34;,&#34;v6ZMCJaK&#34;,&#34;LtlRT6YZ&#34;,&#34;Exe7HkrM&#34;,&#34;NsX3eTna&#34;,&#34;ju6UcWgr&#34;,&#34;R9PQqimD&#34;,&#34;oNuXfW8k&#34;,&#34;XbzXgEyy&#34;,&#34;DxIUtKcJ&#34;,&#34;WivtH2CA&#34;,&#34;ywUND7sh&#34;,&#34;OronH9DG&#34;,&#34;ajDKwax4&#34;,&#34;o4yqJzhe&#34;,&#34;G7ub7HoU&#34;,&#34;qojexwBh&#34;,&#34;7eTB6gjC&#34;,&#34;phacD1ku&#34;,&#34;tgbHM3DM&#34;,&#34;DyQrMaE6&#34;,&#34;OCOBxvTt&#34;,&#34;bfO7ej8e&#34;,&#34;xCfhuDx3&#34;,&#34;r8Tg0xzx&#34;,&#34;uoqPLio7&#34;,&#34;U4b7dD9K&#34;,&#34;2XUDkiVT&#34;,&#34;O21C9ZrU&#34;,&#34;RsvoKktQ&#34;,&#34;MJiIQzJv&#34;,&#34;85nE58d9&#34;,&#34;R6cGtUyR&#34;,&#34;IoqCVkVb&#34;,&#34;NNQerRrG&#34;,&#34;Vz3brQy2&#34;,&#34;mVqPRs4a&#34;,&#34;B5kV9AlC&#34;,&#34;5uMeBi4q&#34;,&#34;iGHfbsJY&#34;,&#34;imcQdQHM&#34;,&#34;P2H2sxjR&#34;,&#34;isXZMzqi&#34;,&#34;DeZXvdcv&#34;,&#34;ylPGxA3d&#34;,&#34;q4deZjXX&#34;,&#34;AvQJ80wP&#34;,&#34;tgh0q4bA&#34;,&#34;xscW48Y8&#34;,&#34;3f4faA7S&#34;,&#34;Q1yHdiJP&#34;],&#34;widget&#34;:&#34;select&#34;,&#34;size&#34;:1}]"></div>
</div>
</td>


<td class="td cbi-value-field" data-name="port" data-widget="value" data-title="Socks Listen Port">
<div id="cbi-passwall2-3hhWmggO-port" data-index="" data-depends="[]">




	<div data-ui-widget="[&#34;Textfield&#34;,&#34;10001&#34;,{&#34;optional&#34;:false,&#34;id&#34;:&#34;cbid.passwall2.3hhWmggO.port&#34;,&#34;datatype&#34;:&#34;port&#34;,&#34;name&#34;:&#34;cbid.passwall2.3hhWmggO.port&#34;}]"></div>


</div>
</td>


<td class="td cbi-value-field" data-name="http_port" data-widget="value" data-title="HTTP Listen Port 0 is not use">
<div id="cbi-passwall2-3hhWmggO-http_port" data-index="" data-depends="[]">




	<div data-ui-widget="[&#34;Textfield&#34;,&#34;0&#34;,{&#34;optional&#34;:true,&#34;id&#34;:&#34;cbid.passwall2.3hhWmggO.http_port&#34;,&#34;datatype&#34;:&#34;port&#34;,&#34;name&#34;:&#34;cbid.passwall2.3hhWmggO.http_port&#34;}]"></div>


</div>
</td>


<td class="td cbi-section-table-cell nowrap cbi-section-actions">
					<div><input class="btn cbi-button cbi-button-edit" type="button" value="Edit" onclick="location.href='/cgi-bin/luci/admin/services/passwall2/socks_config/3hhWmggO'" alt="Edit" title="Edit" />
						
							<input class="btn cbi-button cbi-button-remove" type="submit" value="Delete"  onclick="this.form.cbi_state='del-section'; return true" name="cbi.rts.passwall2.3hhWmggO" alt="Delete" title="Delete" /></div>
				</td></tr></table>

	<div class="cbi-section-create cbi-tblsection-create">
			
				<input class="btn cbi-button cbi-button-add" type="submit" value="Add" name="cbi.cts.passwall2.socks.3hhWmggO" title="Add" />
			
		</div></div>
<!-- /tblsection --><script type="text/javascript">
	//<![CDATA[
	function go() {
		var _status = document.getElementsByClassName('_status');
		for (var i = 0; i < _status.length; i++) {
			var id = _status[i].getAttribute("socks_id");
			XHR.get('/cgi-bin/luci/admin/services/passwall2/socks_status', {
					index: i,
					id: id
				},
				function(x, result) {
					var index = result.index;
					var div = '';
					var div1 = '<font style="font-weight:bold;" color="green">â</font>&nbsp';
					var div2 = '<font style="font-weight:bold;" color="red">X</font>&nbsp';
					
					if (result.socks_status) {
						div += div1;
					} else {
						div += div2;
					}
					if (result.use_http) {
						if (result.http_status) {
							div += div1;
						} else {
							div += div2;
						}
					}
					_status[index].innerHTML = div;
				}
			);
		}
		
		var global_id = null;
		var global = document.getElementById("cbi-passwall2-global");
		if (global) {
			var node = global.getElementsByClassName("cbi-section-node")[0];
			var node_id = node.getAttribute("id");
			global_id = node_id;
			var reg1 = new RegExp("(?<=" + node_id + "-).*?(?=(node))")
			for (var i = 0; i < node.childNodes.length; i++) {
				if (node.childNodes[i].childNodes && node.childNodes[i].childNodes.length > 0) {
					for (var k = 0; k < node.childNodes[i].childNodes.length; k++) {
						try {
							var dom = node.childNodes[i].childNodes[k];
							if (dom.id) {
								var s = dom.id.match(reg1);
								if (s) {
									var cbi_id = global_id + "-"
									var dom_id = dom.id.split(cbi_id).join(cbi_id.split("-").join(".")).split("cbi.").join("cbid.")
									var node_select = document.getElementsByName(dom_id)[0];
									var node_select_value = node_select.value;
									if (node_select_value && node_select_value != "nil" && node_select_value.indexOf("_default") != 0 && node_select_value.indexOf("_direct") != 0 && node_select_value.indexOf("_blackhole") != 0) {
										if (node_select.tagName == "INPUT") {
											node_select = document.getElementById("cbi.combobox." + dom_id);
										}
										
										if (true) {
											var to_url = "/cgi-bin/luci/admin/services/passwall2/node_config/" + node_select_value;
											if (node_select_value.indexOf("Socks_") == 0) {
												to_url = "/cgi-bin/luci/admin/services/passwall2/socks_config/" + node_select_value.substring("Socks_".length);
											}
											var new_a = document.createElement("a");
											new_a.innerHTML = "Edit";
											new_a.href = "#";
											new_a.setAttribute("onclick", "location.href='" + to_url + "'");
											var new_html = new_a.outerHTML;
										}
										
										if (s[0] == "") {
											var log_a = document.createElement("a");
											log_a.innerHTML = "Log";
											log_a.href = "#";
											log_a.setAttribute("onclick", "window.open('" + '/cgi-bin/luci/admin/services/passwall2/get_redir_log' + "?id=default&name=global" + "', '_blank')");
											new_html += "&nbsp&nbsp" + log_a.outerHTML;
										}
										
										node_select.insertAdjacentHTML("afterend", "&nbsp&nbsp" + new_html);
									}
								}
							}
						} catch(err) {
						}
					}
				}
			}
		}
		
		var socks = document.getElementById("cbi-passwall2-socks");
		if (socks) {
			var socks_enabled_dom = document.getElementById(global_id + "-socks_enabled");
			socks_enabled_dom.parentNode.removeChild(socks_enabled_dom);
			var descr = socks.getElementsByClassName("cbi-section-descr")[0];
			descr.outerHTML = socks_enabled_dom.outerHTML;
			rows = socks.getElementsByClassName("cbi-section-table-row");
			for (var i = 0; i < rows.length; i++) {
				try {
					var row = rows[i];
					var id = row.id;
					if (!id) continue;
					var dom_id = id + "-node";
					var node = document.getElementById(dom_id);
					var dom_id = dom_id.replace("cbi-", "cbid-").replace(new RegExp("-", 'g'), ".");
					var node_select = document.getElementsByName(dom_id)[0];
					var node_select_value = node_select.value;
					if (node_select_value && node_select_value != "nil") {
						var v = document.getElementById(dom_id + "-" + node_select_value);
						if (v) {
							node_select.title = v.text;
						} else {
							node_select.title = node_select.options[node_select.options.selectedIndex].text;
						}
						

						var new_html = ""

						var new_a = document.createElement("a");
						new_a.innerHTML = "Edit";
						new_a.href = "#";
						new_a.setAttribute("onclick","location.href='" + '/cgi-bin/luci/admin/services/passwall2/node_config' + "/" + node_select_value + "'");
						new_html = new_a.outerHTML;

						var log_a = document.createElement("a");
						log_a.innerHTML = "Log";
						log_a.href = "#";
						log_a.setAttribute("onclick", "window.open('" + '/cgi-bin/luci/admin/services/passwall2/get_socks_log' + "?name=" + id.replace("cbi-passwall2-", "") + "', '_blank')");
						new_html += "&nbsp" + log_a.outerHTML;

						node_select.insertAdjacentHTML("afterend", "&nbsp&nbsp" + new_html);
					}
				} catch(err) {
				}
			}
		}
	}
	setTimeout("go()", 1000);

	//]]>
</script>
	
</div>

<div class="cbi-page-actions"><input class="btn cbi-button cbi-button-apply" type="button" value="Save &#38; Apply" onclick="cbi_submit(this, 'cbi.apply')" /> <input class="btn cbi-button cbi-button-save" type="submit" value="Save" /> <input class="btn cbi-button cbi-button-reset" type="button" value="Reset" onclick="location.href='/cgi-bin/luci/admin/services/passwall2/settings'" /> </div>

</form>

<script type="text/javascript">cbi_init();</script>


</div>
<footer class="mobile-hide">
	<div>
		<a class="luci-link" href="https://github.com/openwrt/luci">Powered by LuCI istoreos-22.03 branch (git-24.339.46321-5f9267c)</a> /
						<a href="https://github.com/jerrykuku/luci-theme-argon">ArgonTheme v*********</a> /
						iStoreOS 22.03.7 2024122712
		<ul class="breadcrumb pull-right" id="modemenu" style="display:none"></ul>
	</div>
</footer>
</div>
</div>
<script>
	// thanks for Jo-Philipp Wich <<EMAIL>>
	var luciLocation = ["admin","services","passwall2","settings"];
	var winHeight = $(window).height();
	$(window).resize(function () {
		var winWidth = $(window).width()
		if(winWidth < 600){
			var newHeight = $(this).height();
			var keyboradHeight = newHeight - winHeight;
			$(".ftc").css("bottom", keyboradHeight + 30);
		}
	})
</script>
<script type="text/javascript">L.require('menu-argon', null, '*********')</script>
</body>
</html>


