#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI配置功能
"""

from ui_config import UIConfig

def test_ui_config():
    """测试UI配置功能"""
    print("🧪 开始测试UI配置功能...")
    print("=" * 80)
    
    # 测试窗口设置
    print("📐 窗口设置:")
    print(f"  窗口几何尺寸: {UIConfig.get_window_geometry()}")
    print(f"  窗口可调整大小: {UIConfig.get_window_resizable()}")
    print()
    
    # 测试选择框默认状态
    print("☑️ 选择框默认状态:")
    print(f"  二次登录重新提交: {UIConfig.get_retry_login_default()}")
    print(f"  保存配置创建文件: {UIConfig.get_save_create_files_default()}")
    print(f"  保存配置删除文件: {UIConfig.get_save_delete_files_default()}")
    print(f"  保存token文件: {UIConfig.get_save_token_files_default()}")
    print(f"  保存批量提交文件: {UIConfig.get_save_batch_files_default()}")
    print()
    
    # 测试单选框默认状态
    print("🔘 单选框默认状态:")
    print(f"  处理模式: {UIConfig.get_processing_mode_default()}")
    print()
    
    # 测试输入框默认值
    print("📝 输入框默认值:")
    print(f"  节点数量限制: {UIConfig.get_node_limit_default()}")
    print(f"  分批大小: {UIConfig.get_batch_size_default()}")
    print()
    
    # 测试文本框设置
    print("📄 文本框设置:")
    print(f"  Socks配置文本框高度: {UIConfig.get_socks_text_height()}")
    print(f"  日志文本框高度: {UIConfig.get_log_text_height()}")
    print()
    
    print("✅ UI配置功能测试完成！")

def demo_config_changes():
    """演示配置修改示例"""
    print("\n" + "=" * 80)
    print("🔧 配置修改示例演示:")
    print("=" * 80)
    
    print("💡 要修改配置，请编辑 ui_config.py 文件中对应的方法返回值")
    print()
    
    print("📋 常用配置示例:")
    print()
    
    print("1️⃣ 开发调试模式（启用所有调试文件）:")
    print("   修改以下方法返回 True:")
    print("   - get_save_create_files_default()")
    print("   - get_save_delete_files_default()")
    print("   - get_save_token_files_default()")
    print("   - get_save_batch_files_default()")
    print()
    
    print("2️⃣ 测试模式（限制6个节点）:")
    print("   - get_node_limit_default() 返回 '6'")
    print("   - get_processing_mode_default() 返回 'limit'")
    print()
    
    print("3️⃣ 批量模式（每批20个节点）:")
    print("   - get_batch_size_default() 返回 '20'")
    print("   - get_processing_mode_default() 返回 'batch'")
    print()
    
    print("4️⃣ 紧凑窗口模式:")
    print("   - get_window_geometry() 返回 '500x700'")
    print()
    
    print("5️⃣ 大窗口模式:")
    print("   - get_window_geometry() 返回 '600x1000'")
    print()
    
    print("6️⃣ 禁用二次登录:")
    print("   - get_retry_login_default() 返回 False")
    print()

def test_import_in_main():
    """测试在main.py中的导入"""
    print("\n" + "=" * 80)
    print("🔗 测试main.py导入:")
    print("=" * 80)
    
    try:
        # 模拟main.py中的导入
        import tkinter as tk
        from ui_config import UIConfig
        
        print("✅ 成功导入UIConfig")
        
        # 测试创建窗口
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry(UIConfig.get_window_geometry())
        resizable_settings = UIConfig.get_window_resizable()
        root.resizable(resizable_settings[0], resizable_settings[1])
        
        print(f"✅ 成功设置窗口几何尺寸: {UIConfig.get_window_geometry()}")
        print(f"✅ 成功设置窗口可调整大小: {UIConfig.get_window_resizable()}")
        
        # 测试变量创建
        retry_var = tk.BooleanVar(value=UIConfig.get_retry_login_default())
        mode_var = tk.StringVar(value=UIConfig.get_processing_mode_default())
        limit_var = tk.StringVar(value=UIConfig.get_node_limit_default())
        
        print(f"✅ 成功创建变量:")
        print(f"   retry_var: {retry_var.get()}")
        print(f"   mode_var: {mode_var.get()}")
        print(f"   limit_var: {limit_var.get()}")
        
        # 关闭测试窗口
        root.destroy()
        
        print("✅ main.py导入测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ main.py导入测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始UI配置功能完整测试")
    print("=" * 80)
    
    # 基础配置测试
    test_ui_config()
    
    # 配置修改示例
    demo_config_changes()
    
    # main.py导入测试
    import_success = test_import_in_main()
    
    print("\n" + "=" * 80)
    print("🏁 测试完成")
    
    if import_success:
        print("🎉 所有测试通过！UI配置功能已成功实现。")
        print("💡 现在您可以通过修改 ui_config.py 来调整界面默认设置。")
    else:
        print("⚠️ 部分测试失败，请检查代码。")
