提交包：
POST http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/iZbUYFug HTTP/1.1
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: max-age=0
Content-Length: 1457
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryhabCBqgi2aWqRIHt
Cookie: sysauth_http=f21191692f5a4dad6b9ba3cc72e3b6b5
Host: *************
Origin: http://*************
Proxy-Connection: keep-alive
Referer: http://*************/cgi-bin/luci/admin/services/passwall2/socks_config/iZbUYFug
Upgrade-Insecure-Requests: 1
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="token"

910459f039c434f0bf857599f929a1a3
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbi.submit"

1
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbi.cbe.passwall2.iZbUYFug.enabled"

1
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbid.passwall2.iZbUYFug.enabled"

1
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbid.passwall2.iZbUYFug.node"

NHbySajH
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbi.cbe.passwall2.iZbUYFug.bind_local"

1
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbid.passwall2.iZbUYFug.port"

1081
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbid.passwall2.iZbUYFug.http_port"

0
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbi.cbe.passwall2.iZbUYFug.log"

1
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbid.passwall2.iZbUYFug.log"

1
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbi.cbe.passwall2.iZbUYFug.enable_autoswitch"

1
------WebKitFormBoundaryhabCBqgi2aWqRIHt
Content-Disposition: form-data; name="cbi.apply"

1
------WebKitFormBoundaryhabCBqgi2aWqRIHt--
