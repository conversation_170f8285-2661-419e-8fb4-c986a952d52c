#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特殊符号清理功能
"""

from html_parser import PasswallHtmlParser

def test_special_symbols_cleaning():
    """测试特殊符号清理功能"""
    parser = PasswallHtmlParser()
    
    # 测试用例：包含各种特殊符号的节点名称
    test_cases = [
        {
            'input': 'µ Japan 01',
            'expected': 'Japan 01',
            'description': '测试µ符号清理'
        },
        {
            'input': 'º Hong Kong 02',
            'expected': 'Hong Kong 02',
            'description': '测试º符号清理'
        },
        {
            'input': '°Singapore 03',
            'expected': 'Singapore 03',
            'description': '测试°符号清理'
        },
        {
            'input': '±USA 04²',
            'expected': 'USA 04',
            'description': '测试±和²符号清理'
        },
        {
            'input': 'Xray：µ Germany 05º',
            'expected': 'Germany 05',
            'description': '测试前缀和多个特殊符号清理'
        },
        {
            'input': '×Korea 06÷',
            'expected': 'Korea 06',
            'description': '测试×和÷符号清理'
        },
        {
            'input': 'àáâãäå Netherlands 07',
            'expected': 'Netherlands 07',
            'description': '测试拉丁字符清理'
        },
        {
            'input': 'µºº°±²³¹¼½¾×÷ Taiwan 08',
            'expected': 'Taiwan 08',
            'description': '测试多个特殊符号组合清理'
        },
        {
            'input': 'V2ray Trojan：µ Canada 09º',
            'expected': 'Canada 09',
            'description': '测试复杂前缀和特殊符号清理'
        },
        {
            'input': '   µ   Australia   10   º   ',
            'expected': 'Australia 10',
            'description': '测试空格和特殊符号清理'
        }
    ]
    
    print("🧪 开始测试特殊符号清理功能...")
    print("=" * 80)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        input_name = test_case['input']
        expected = test_case['expected']
        description = test_case['description']
        
        # 执行清理
        result = parser._clean_node_name(input_name)
        
        # 检查结果
        is_success = result == expected
        status = "✅ 通过" if is_success else "❌ 失败"
        
        if is_success:
            success_count += 1
        
        print(f"测试 {i:2d}: {description}")
        print(f"  输入: '{input_name}'")
        print(f"  期望: '{expected}'")
        print(f"  结果: '{result}'")
        print(f"  状态: {status}")
        print("-" * 80)
    
    # 输出总结
    print(f"\n📊 测试总结:")
    print(f"  总测试数: {total_count}")
    print(f"  成功数量: {success_count}")
    print(f"  失败数量: {total_count - success_count}")
    print(f"  成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试通过！特殊符号清理功能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查清理逻辑。")
    
    return success_count == total_count

def test_nodes_data_update():
    """测试节点数据更新功能"""
    print("\n🔄 测试节点数据更新功能...")
    
    # 模拟包含特殊符号的节点数据
    test_nodes = [
        {'id': 'test1', 'name': 'µ Japan 01', 'region': '日本'},
        {'id': 'test2', 'name': 'º Hong Kong 02', 'region': '香港'},
        {'id': 'test3', 'name': '°Singapore 03', 'region': '新加坡'},
        {'id': 'test4', 'name': '±USA 04²', 'region': '美国'},
        {'id': 'test5', 'name': 'Xray：µ Germany 05º', 'region': '德国'}
    ]
    
    parser = PasswallHtmlParser()
    
    print("原始节点数据:")
    for node in test_nodes:
        print(f"  {node['id']}: '{node['name']}'")
    
    # 清理节点名称
    cleaned_nodes = []
    for node in test_nodes:
        cleaned_node = node.copy()
        cleaned_node['name'] = parser._clean_node_name(node['name'])
        cleaned_nodes.append(cleaned_node)
    
    print("\n清理后的节点数据:")
    for node in cleaned_nodes:
        print(f"  {node['id']}: '{node['name']}'")
    
    # 保存到JSON文件进行测试
    try:
        parser._save_nodes_to_json(cleaned_nodes)
        print("\n✅ 节点数据已保存到 nodes_data.json")
        return True
    except Exception as e:
        print(f"\n❌ 保存节点数据失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始特殊符号清理功能测试")
    print("=" * 80)
    
    # 测试特殊符号清理
    test1_result = test_special_symbols_cleaning()
    
    # 测试节点数据更新
    test2_result = test_nodes_data_update()
    
    print("\n" + "=" * 80)
    print("🏁 测试完成")
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！特殊符号清理功能已成功实现。")
    else:
        print("⚠️ 部分测试失败，请检查代码。")
