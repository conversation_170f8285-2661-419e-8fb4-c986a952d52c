#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志记录模块
记录所有HTTP请求和响应的详细信息
"""

import os
import datetime
import json

class RequestLogger:
    def __init__(self, log_file="日志.txt"):
        self.log_file = log_file
        self.ensure_log_file()
        
    def ensure_log_file(self):
        """确保日志文件存在"""
        if not os.path.exists(self.log_file):
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write("OpenWrt Passwall 工具运行日志\n")
                f.write("=" * 50 + "\n\n")
    
    def log_request(self, method, url, headers=None, data=None, params=None, files=None):
        """记录HTTP请求"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        log_content = f"\n[{timestamp}] === HTTP请求 ===\n"
        log_content += f"方法: {method}\n"
        log_content += f"URL: {url}\n"

        if headers:
            log_content += "请求头:\n"
            for key, value in headers.items():
                log_content += f"  {key}: {value}\n"

        if params:
            log_content += f"URL参数: {params}\n"

        if data:
            if isinstance(data, dict):
                log_content += "POST数据:\n"
                for key, value in data.items():
                    # 隐藏密码
                    if 'password' in key.lower():
                        value = '*' * len(str(value))
                    log_content += f"  {key}: {value}\n"
            else:
                log_content += f"POST数据: {data}\n"

        if files:
            log_content += "multipart/form-data 字段:\n"
            for key, value in files.items():
                # files格式: {key: (filename, content)}
                if isinstance(value, tuple) and len(value) >= 2:
                    content = value[1]
                    # 隐藏密码
                    if 'password' in key.lower():
                        content = '*' * len(str(content))
                    log_content += f"  {key}: {content}\n"
                else:
                    log_content += f"  {key}: {value}\n"

        log_content += "-" * 30 + "\n"

        self._write_log(log_content)
        
    def log_response(self, response, description=""):
        """记录HTTP响应"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        log_content = f"[{timestamp}] === HTTP响应 {description} ===\n"
        log_content += f"状态码: {response.status_code}\n"
        log_content += f"状态文本: {response.reason}\n"
        
        log_content += "响应头:\n"
        for key, value in response.headers.items():
            log_content += f"  {key}: {value}\n"
            
        # 特别关注Set-Cookie头
        if 'Set-Cookie' in response.headers:
            log_content += f"\n🍪 重要Cookie信息:\n"
            cookie_header = response.headers['Set-Cookie']
            log_content += f"  完整Cookie: {cookie_header}\n"
            
            # 提取sysauth_http
            import re
            session_match = re.search(r'sysauth_http=([^;]+)', cookie_header)
            if session_match:
                session_id = session_match.group(1)
                log_content += f"  ✅ 提取到sysauth_http: {session_id}\n"
            else:
                log_content += f"  ❌ 未找到sysauth_http\n"
        
        # 记录响应内容（限制长度）
        if hasattr(response, 'text'):
            content = response.text
            if len(content) > 1000:
                log_content += f"\n响应内容（前1000字符）:\n{content[:1000]}...\n"
            else:
                log_content += f"\n响应内容:\n{content}\n"
        
        log_content += "=" * 50 + "\n\n"
        
        self._write_log(log_content)
        
    def log_message(self, message, level="INFO"):
        """记录普通消息"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_content = f"[{timestamp}] [{level}] {message}\n"
        self._write_log(log_content)
        
    def log_error(self, error, context=""):
        """记录错误信息"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_content = f"[{timestamp}] [ERROR] {context}\n"
        log_content += f"错误详情: {str(error)}\n"
        log_content += "-" * 30 + "\n"
        self._write_log(log_content)
        
    def _write_log(self, content):
        """写入日志文件"""
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(content)
                f.flush()  # 立即刷新到磁盘
        except Exception as e:
            print(f"写入日志失败: {e}")
            
    def clear_log(self):
        """清空日志文件"""
        try:
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write("OpenWrt Passwall 工具运行日志\n")
                f.write("=" * 50 + "\n\n")
        except Exception as e:
            print(f"清空日志失败: {e}")

# 全局日志实例
request_logger = RequestLogger()
