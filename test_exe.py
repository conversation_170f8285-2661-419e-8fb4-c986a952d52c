#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试exe程序是否能正常运行
"""

import os
import subprocess
import time
import sys

def test_exe():
    """测试exe程序"""
    print("🧪 测试exe程序...")
    
    exe_path = os.path.join('dist', 'OpenWrt_Passwall_Socks_配置工具.exe')
    
    if not os.path.exists(exe_path):
        print(f"❌ 错误: 未找到exe文件: {exe_path}")
        return False
    
    print(f"📁 exe文件路径: {exe_path}")
    print(f"📊 exe文件大小: {os.path.getsize(exe_path) / (1024*1024):.1f} MB")
    
    try:
        print("🚀 启动exe程序...")
        print("💡 程序将在5秒后自动关闭，请观察是否正常启动")
        
        # 启动exe程序（非阻塞）
        process = subprocess.Popen([exe_path])
        
        # 等待5秒
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 程序正常启动！")
            
            # 终止进程
            process.terminate()
            time.sleep(1)
            
            # 如果还没终止，强制杀死
            if process.poll() is None:
                process.kill()
            
            print("✅ 程序已正常关闭")
            return True
        else:
            print(f"❌ 程序启动后立即退出，退出码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 启动exe程序时发生异常: {e}")
        return False

def create_distribution_package():
    """创建分发包"""
    print("\n📦 创建分发包...")
    
    import zipfile
    from datetime import datetime
    
    # 创建zip文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_name = f"OpenWrt_Passwall_Socks_配置工具_{timestamp}.zip"
    
    try:
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加dist目录中的所有文件
            for root, dirs, files in os.walk('dist'):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 在zip中的路径（去掉dist前缀）
                    arcname = os.path.relpath(file_path, 'dist')
                    zipf.write(file_path, arcname)
                    print(f"  ✅ 已添加: {arcname}")
        
        zip_size = os.path.getsize(zip_name) / (1024*1024)
        print(f"📦 分发包创建成功: {zip_name} ({zip_size:.1f} MB)")
        return True
        
    except Exception as e:
        print(f"❌ 创建分发包失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 OpenWrt Passwall Socks 配置工具 - exe测试")
    print("=" * 60)
    
    # 检查dist目录
    if not os.path.exists('dist'):
        print("❌ 错误: dist目录不存在，请先运行打包脚本")
        return False
    
    # 测试exe
    if not test_exe():
        print("\n❌ exe测试失败")
        return False
    
    # 创建分发包
    if not create_distribution_package():
        print("\n⚠️ 分发包创建失败，但exe测试成功")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("\n📋 使用说明:")
    print("1. 可以直接运行 dist 目录中的 exe 文件")
    print("2. 如需分发给其他人，可以使用生成的 zip 文件")
    print("3. 首次运行可能需要一些时间来解压和初始化")
    print("4. 确保目标电脑有网络连接和对应的OpenWrt路由器")
    
    print("\n💡 注意事项:")
    print("- exe文件包含了所有必要的依赖，无需安装Python")
    print("- 如果杀毒软件报警，请添加到白名单")
    print("- 程序需要网络权限来连接OpenWrt路由器")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按回车键退出...")
        sys.exit(1)
    else:
        input("\n按回车键退出...")
