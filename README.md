# OpenWrt Passwall Socks 批量配置工具

一个用于批量配置 OpenWrt Passwall2 Socks 代理的图形化工具。

## 功能特性

- 🔐 自动登录 OpenWrt 后台
- 📋 获取现有节点列表
- 🗑️ 删除现有 Socks 配置
- ⚡ 批量创建新的 Socks 配置
- 🌍 根据节点地区自动分配端口
- 📊 实时显示配置进度
- 📝 详细的操作日志

## 端口分配规则

| 地区 | 起始端口 |
|------|----------|
| 香港 | 10000 |
| 美国 | 20000 |
| 日本 | 30000 |
| 新加坡 | 40000 |
| 韩国 | 50000 |
| 台湾 | 60000 |
| 英国 | 70000 |
| 德国 | 80000 |
| 法国 | 90000 |
| 加拿大 | 11000 |
| 其他地区 | 1080 |

## 系统要求

- Python 3.6+
- Windows/Linux/macOS
- 网络连接到 OpenWrt 设备

## 安装和使用

### 方法一：使用启动脚本（推荐）

1. 下载所有文件到同一目录
2. 运行启动脚本：
   ```bash
   python run.py
   ```
   启动脚本会自动检查并安装依赖包。

### 方法二：手动安装依赖

1. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

2. 运行主程序：
   ```bash
   python main.py
   ```

## 使用说明

1. **填写连接信息**
   - OpenWrt地址：默认为 `*************`
   - 用户名：默认为 `root`
   - 密码：输入您的 OpenWrt 管理密码

2. **开始配置**
   - 点击"开始批量配置"按钮
   - 程序将自动执行以下步骤：
     - 登录 OpenWrt 后台
     - 获取节点列表
     - 删除现有 Socks 配置
     - 为每个节点创建新的 Socks 配置

3. **查看进度**
   - 实时日志显示当前操作状态
   - 进度条显示配置完成度

## 配置文件说明

### config.py
包含应用程序的配置参数：
- 默认连接信息
- 地区端口映射
- 请求超时设置

### 自定义配置
您可以修改 `config.py` 中的以下参数：
- `REGION_PORTS`：地区端口映射
- `DEFAULT_PORT`：默认端口
- `REQUEST_TIMEOUT`：请求超时时间

## 文件结构

```
├── main.py              # 主程序入口
├── openwrt_client.py    # OpenWrt 客户端
├── html_parser.py       # HTML 解析器
├── config.py            # 配置文件
├── run.py               # 启动脚本
├── requirements.txt     # 依赖包列表
└── README.md           # 说明文档
```

## 注意事项

1. **网络连接**：确保能够访问 OpenWrt 设备的管理界面
2. **权限要求**：需要 OpenWrt 管理员权限
3. **备份建议**：建议在使用前备份现有配置
4. **防火墙**：确保配置的端口没有被防火墙阻止

## 故障排除

### 登录失败
- 检查 OpenWrt 地址是否正确
- 确认用户名和密码
- 检查网络连接

### 节点获取失败
- 确认 Passwall2 插件已安装
- 检查是否有可用节点
- 尝试手动访问 Passwall2 页面

### 配置创建失败
- 检查端口是否被占用
- 确认节点状态正常
- 查看详细错误日志

## 技术支持

如果遇到问题，请检查：
1. 程序日志中的错误信息
2. OpenWrt 系统日志
3. 网络连接状态

## 免责声明

本工具仅供学习和研究使用，使用者需自行承担使用风险。请遵守当地法律法规。
