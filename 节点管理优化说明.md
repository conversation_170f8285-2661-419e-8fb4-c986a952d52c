# 节点管理优化说明

## 📋 优化概述

本次优化实现了智能节点信息管理功能，解决了节点数据的持久化管理和状态跟踪问题。

## 🎯 核心功能

### 1️⃣ **固定映射关系保持**
- `name` 和 `socks_port` 建立**永久的一一对应关系**
- 一旦节点名称分配了端口号，该映射关系永远不变
- 即使节点的其他信息发生变化，端口号保持不变

### 2️⃣ **智能字段更新**
- **只更新可变字段**：`id`、`address`、`port`
- **保持固定字段**：`name`、`region`、`socks_port`
- 自动检测节点信息变化并更新

### 3️⃣ **新增节点处理**
- 自动检测新出现的节点
- 按照地区端口分配规则分配新的 `socks_port`
- 新节点自动标记为 `is_use: true`

### 4️⃣ **废弃节点检测**
- 自动检测不再存在的节点
- 将废弃节点标记为 `is_use: false`
- 在日志中明确告知用户哪些节点已停用

### 5️⃣ **状态管理**
- 新增 `is_use` 字段控制节点启用状态
- 只处理 `is_use: true` 的节点
- 废弃节点保留在文件中但不参与配置

## 🔧 技术实现

### 核心方法：`assign_ports_to_nodes()`

```python
def assign_ports_to_nodes(self, nodes):
    """智能管理节点信息：与现有nodes_data.json匹配，保持name和socks_port固定映射"""
    
    # 1. 加载现有节点数据
    existing_nodes = self.load_existing_nodes()
    existing_nodes_map = {node['name']: node for node in existing_nodes}
    
    # 2. 处理新获取的节点
    for node in nodes:
        if node_name in existing_nodes_map:
            # 已存在：只更新id、address、port，保持name和socks_port不变
            existing_node = existing_nodes_map[node_name].copy()
            existing_node['id'] = node['id']
            existing_node['address'] = node['address'] 
            existing_node['port'] = node['port']
            existing_node['is_use'] = True
        else:
            # 新增：分配新的socks_port
            new_socks_port = self.assign_new_socks_port(node['region'], all_nodes)
            node['socks_port'] = new_socks_port
            node['is_use'] = True
    
    # 3. 检查废弃的节点
    for existing_node in existing_nodes:
        if existing_node['name'] not in new_node_names:
            existing_node['is_use'] = False  # 标记为废弃
    
    # 4. 只返回启用的节点
    return [node for node in all_nodes if node.get('is_use', True)]
```

### 辅助方法

1. **`load_existing_nodes()`** - 加载现有节点数据，向后兼容
2. **`assign_new_socks_port()`** - 为新节点分配端口号
3. **`save_nodes_data()`** - 保存节点数据到JSON文件

## 📊 数据结构

### 更新后的 nodes_data.json 结构：

```json
[
  {
    "id": "new_hk_01",
    "type": "Xray",
    "name": "Hong Kong 01",
    "address": "new.server1.com",
    "port": "8443",
    "region": "香港",
    "socks_port": 10001,
    "is_use": true
  },
  {
    "id": "old_hk_02",
    "type": "Xray", 
    "name": "Hong Kong 02",
    "address": "old.server2.com",
    "port": "443",
    "region": "香港",
    "socks_port": 10002,
    "is_use": false
  }
]
```

## 🎬 工作流程示例

### 场景：节点信息更新

**现有节点数据：**
- Hong Kong 01 (端口 10001)
- Hong Kong 02 (端口 10002)
- USA Node 01 (端口 20001)

**新获取节点：**
- Hong Kong 01 (地址变化)
- Hong Kong 03 (新增)
- Korea 01 (新增)

**处理结果：**
1. ✅ Hong Kong 01 → 更新地址，保持端口 10001
2. ⚠️ Hong Kong 02 → 标记为 `is_use: false`
3. 🆕 Hong Kong 03 → 分配端口 10003
4. 🆕 Korea 01 → 分配端口 50001
5. ✅ 最终返回 3 个可用节点

## 📝 日志输出示例

```
🔄 开始智能节点信息管理...
📊 现有节点数据: 3 个
📊 新获取节点: 3 个
🔄 更新节点: Hong Kong 01 -> 端口 10001 (保持不变)
🆕 新增节点: Hong Kong 03 -> 端口 10003
🆕 新增节点: Korea 01 -> 端口 50001
⚠️ 废弃节点: Hong Kong 02 -> 端口 10002 (已停用)

📋 节点信息管理完成:
  🔄 更新节点: 1 个
  🆕 新增节点: 2 个
  ⚠️ 废弃节点: 1 个
  📊 总计节点: 4 个
  🚨 已停用的节点: Hong Kong 02
  ✅ 可用节点: 3 个
```

## 🧪 测试验证

### 测试脚本
- `test_node_management.py` - 基础功能测试
- `test_complete_workflow.py` - 完整工作流程测试

### 测试覆盖
- ✅ 节点信息更新
- ✅ 新增节点处理
- ✅ 废弃节点检测
- ✅ 端口分配逻辑
- ✅ 状态管理
- ✅ 向后兼容性

## 🔄 向后兼容

- 自动为旧数据添加 `is_use: true` 字段
- 保持原有端口分配逻辑
- 不影响现有配置文件

## 💡 使用建议

1. **首次使用**：程序会自动创建 `nodes_data.json` 文件
2. **定期备份**：建议定期备份 `nodes_data.json` 文件
3. **手动管理**：可手动编辑 `is_use` 字段来启用/禁用特定节点
4. **监控日志**：关注废弃节点提示，及时了解节点变化

## 🎉 优化效果

- ✅ **数据持久化**：节点配置信息永久保存
- ✅ **智能更新**：自动处理节点信息变化
- ✅ **状态管理**：清晰的节点启用/禁用状态
- ✅ **用户友好**：详细的日志提示和状态报告
- ✅ **性能优化**：只处理启用的节点，提高效率

---

**版本**: 1.0.0  
**更新时间**: 2025-08-04  
**兼容性**: 向后兼容所有现有配置
