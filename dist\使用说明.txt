# OpenWrt Passwall Socks 批量配置工具

## 📋 使用说明

### 🚀 快速开始
1. 双击运行 `OpenWrt_Passwall_Socks_配置工具.exe`
2. 根据需要调整配置选项
3. 点击"开始批量配置"按钮

### ⚙️ 配置说明

#### 处理模式
- **限制模式**: 只处理前N个节点，适合测试
- **分批模式**: 将所有节点分批处理，适合大量节点

#### 调试文件选项
- 默认情况下不保存调试文件，节省磁盘空间
- 如需调试问题，可勾选相应的调试文件保存选项

#### 二次登录重新提交
- 当批量提交失败时，自动退出登录重新登录再试一次
- 建议保持开启以提高成功率

### 🔧 自定义配置

如需修改默认设置，请：
1. 解压程序到文件夹
2. 编辑 `ui_config.py` 文件
3. 修改对应方法的返回值
4. 重新运行程序

详细配置说明请参考 `UI配置优化说明.md`

### 📞 技术支持

如遇到问题，请检查：
1. OpenWrt路由器是否正常运行
2. 网络连接是否正常
3. Passwall2插件是否已安装

### 📄 版本信息

- 版本: 1.0.0
- 构建时间: 1754275275.3531213
- Python版本: 3.12.7 (tags/v3.12.7:0b05ead, Oct  1 2024, 03:06:41) [MSC v.1941 64 bit (AMD64)]

---
© 2024 OpenWrt Passwall Socks 批量配置工具
