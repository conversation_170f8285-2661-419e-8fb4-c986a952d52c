#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenWrt Passwall Socks 批量配置工具
主程序入口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime
from openwrt_client import OpenWrtClient
from config import Config
from logger import request_logger
from ui_config import UIConfig
from token_dialog import TokenInputDialog

class PasswallSocksApp:
    def __init__(self, root):
        self.root = root
        self.root.title("OpenWrt Passwall Socks 批量配置工具")
        self.root.geometry(UIConfig.get_window_geometry())
        resizable_settings = UIConfig.get_window_resizable()
        self.root.resizable(resizable_settings[0], resizable_settings[1])
        
        # 初始化客户端
        self.client = None
        self.is_running = False
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        # 标题
        title_frame = tk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        
        title_label = tk.Label(title_frame, text="OpenWrt Passwall Socks 批量配置工具",
                              font=("微软雅黑", 14, "bold"))
        title_label.pack()
        
        # 分隔线
        separator1 = ttk.Separator(self.root, orient='horizontal')
        separator1.pack(fill=tk.X, padx=10, pady=5)

        # 二次登录重新提交选项和网页token选项
        retry_frame = tk.Frame(self.root)
        retry_frame.pack(fill=tk.X, padx=10, pady=5)

        # 左侧：启用二次登录重新提交
        self.enable_retry_login = tk.BooleanVar(value=UIConfig.get_retry_login_default())
        retry_checkbox = tk.Checkbutton(retry_frame, text="启用二次登录重新提交",
                                       variable=self.enable_retry_login,
                                       font=("微软雅黑", 10))
        retry_checkbox.pack(side=tk.LEFT, anchor=tk.W)

        # 右侧：启用网页token
        self.enable_web_token = tk.BooleanVar(value=UIConfig.get_web_token_default())
        web_token_checkbox = tk.Checkbutton(retry_frame, text="启用网页token",
                                           variable=self.enable_web_token,
                                           font=("微软雅黑", 10))
        web_token_checkbox.pack(side=tk.LEFT, anchor=tk.W, padx=(20, 0))

        # 调试文件保存选项
        debug_frame = tk.Frame(self.root)
        debug_frame.pack(fill=tk.X, padx=10, pady=5)

        debug_label = tk.Label(debug_frame, text="调试文件保存选项：", font=("微软雅黑", 10, "bold"))
        debug_label.pack(anchor=tk.W)

        # 4个独立的调试文件选择框
        self.save_create_files = tk.BooleanVar(value=UIConfig.get_save_create_files_default())
        create_checkbox = tk.Checkbutton(debug_frame, text="保存配置创建请求包和响应包",
                                        variable=self.save_create_files,
                                        font=("微软雅黑", 9))
        create_checkbox.pack(anchor=tk.W, padx=20)

        self.save_delete_files = tk.BooleanVar(value=UIConfig.get_save_delete_files_default())
        delete_checkbox = tk.Checkbutton(debug_frame, text="保存配置删除请求包",
                                        variable=self.save_delete_files,
                                        font=("微软雅黑", 9))
        delete_checkbox.pack(anchor=tk.W, padx=20)

        self.save_token_files = tk.BooleanVar(value=UIConfig.get_save_token_files_default())
        token_checkbox = tk.Checkbutton(debug_frame, text="保存token页面响应",
                                       variable=self.save_token_files,
                                       font=("微软雅黑", 9))
        token_checkbox.pack(anchor=tk.W, padx=20)

        self.save_batch_files = tk.BooleanVar(value=UIConfig.get_save_batch_files_default())
        batch_checkbox = tk.Checkbutton(debug_frame, text="保存批量配置提交请求包和响应包",
                                       variable=self.save_batch_files,
                                       font=("微软雅黑", 9))
        batch_checkbox.pack(anchor=tk.W, padx=20)

        # 处理模式选择
        mode_frame = tk.Frame(self.root)
        mode_frame.pack(fill=tk.X, padx=10, pady=5)

        mode_label = tk.Label(mode_frame, text="处理模式：", font=("微软雅黑", 10, "bold"))
        mode_label.pack(anchor=tk.W)

        # 单选框变量
        self.processing_mode = tk.StringVar(value=UIConfig.get_processing_mode_default())

        # 限制模式单选框和设置
        limit_mode_frame = tk.Frame(mode_frame)
        limit_mode_frame.pack(fill=tk.X, pady=2)

        self.limit_radio = tk.Radiobutton(limit_mode_frame, text="限制模式",
                                         variable=self.processing_mode, value="limit",
                                         command=self.on_mode_change, font=("微软雅黑", 10))
        self.limit_radio.pack(side=tk.LEFT)

        limit_label = tk.Label(limit_mode_frame, text="节点数量限制：", font=("微软雅黑", 10))
        limit_label.pack(side=tk.LEFT, padx=(20, 5))

        self.node_limit_var = tk.StringVar(value=UIConfig.get_node_limit_default())
        self.limit_entry = tk.Entry(limit_mode_frame, textvariable=self.node_limit_var,
                                   font=("微软雅黑", 10), width=10)
        self.limit_entry.pack(side=tk.LEFT, padx=(0, 10))

        limit_help = tk.Label(limit_mode_frame, text="（0=处理所有节点，6=只处理前6个，20=只处理前20个）",
                             font=("微软雅黑", 9), fg="gray")
        limit_help.pack(side=tk.LEFT)

        # 分批模式单选框和设置
        batch_mode_frame = tk.Frame(mode_frame)
        batch_mode_frame.pack(fill=tk.X, pady=2)

        self.batch_radio = tk.Radiobutton(batch_mode_frame, text="分批模式",
                                         variable=self.processing_mode, value="batch",
                                         command=self.on_mode_change, font=("微软雅黑", 10))
        self.batch_radio.pack(side=tk.LEFT)

        batch_label = tk.Label(batch_mode_frame, text="每批数量：", font=("微软雅黑", 10))
        batch_label.pack(side=tk.LEFT, padx=(20, 5))

        self.batch_size_var = tk.StringVar(value=UIConfig.get_batch_size_default())
        self.batch_entry = tk.Entry(batch_mode_frame, textvariable=self.batch_size_var,
                                   font=("微软雅黑", 10), width=10)
        self.batch_entry.pack(side=tk.LEFT, padx=(0, 10))

        batch_help = tk.Label(batch_mode_frame, text="（将所有节点分批处理，如40表示每批处理40个节点）",
                             font=("微软雅黑", 9), fg="gray")
        batch_help.pack(side=tk.LEFT)

        # 初始化界面状态
        self.on_mode_change()

        # 按钮区域
        button_frame = tk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # 创建水平按钮容器
        button_container = tk.Frame(button_frame)
        button_container.pack(fill=tk.X)

        # 开始批量配置按钮
        self.start_button = tk.Button(button_container, text="开始批量配置",
                                     command=self.start_batch_config,
                                     bg="#4CAF50", fg="white",
                                     font=("微软雅黑", 10, "bold"),
                                     height=2, width=15)
        self.start_button.pack(side=tk.LEFT, padx=(0, 5), expand=True, fill=tk.X)

        # 导出socks配置按钮
        self.export_button = tk.Button(button_container, text="导出socks配置",
                                      command=self.export_socks_config,
                                      bg="#2196F3", fg="white",
                                      font=("微软雅黑", 10, "bold"),
                                      height=2, width=15)
        self.export_button.pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)

        # 更新节点按钮
        self.update_nodes_button = tk.Button(button_container, text="更新节点",
                                           command=self.update_nodes,
                                           bg="#FF9800", fg="white",
                                           font=("微软雅黑", 10, "bold"),
                                           height=2, width=15)
        self.update_nodes_button.pack(side=tk.LEFT, padx=(5, 0), expand=True, fill=tk.X)

        # Socks配置显示区域
        socks_frame = tk.Frame(self.root)
        socks_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        tk.Label(socks_frame, text="Socks配置列表:", font=("微软雅黑", 10, "bold")).pack(anchor=tk.W)

        # 创建socks配置文本框和滚动条
        socks_text_frame = tk.Frame(socks_frame)
        socks_text_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.socks_text = tk.Text(socks_text_frame, height=UIConfig.get_socks_text_height(), wrap=tk.WORD,
                                 font=("Consolas", 9))
        socks_scrollbar = tk.Scrollbar(socks_text_frame, orient=tk.VERTICAL, command=self.socks_text.yview)
        self.socks_text.configure(yscrollcommand=socks_scrollbar.set)

        self.socks_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        socks_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 日志显示区域
        log_frame = tk.Frame(self.root)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        tk.Label(log_frame, text="操作日志:", font=("微软雅黑", 10, "bold")).pack(anchor=tk.W)
        
        # 创建日志文本框和滚动条
        log_text_frame = tk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.log_text = tk.Text(log_text_frame, height=UIConfig.get_log_text_height(), wrap=tk.WORD,
                               font=("Consolas", 9))
        scrollbar = tk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 进度条
        progress_frame = tk.Frame(self.root)
        progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(progress_frame, text="进度:", font=("微软雅黑", 10, "bold")).pack(anchor=tk.W)
        
        self.progress_var = tk.StringVar()
        self.progress_var.set("等待开始...")
        self.progress_label = tk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.pack(anchor=tk.W, pady=2)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=2)

        # 测试功能区域
        test_frame = tk.LabelFrame(self.root, text="测试功能", font=("微软雅黑", 10, "bold"))
        test_frame.pack(fill=tk.X, padx=10, pady=5)

        test_desc = tk.Label(test_frame, text="分步骤测试各个功能，方便问题定位和调试",
                            font=("微软雅黑", 9), fg="gray")
        test_desc.pack(anchor=tk.W, padx=5, pady=2)

        # 测试按钮容器
        test_buttons_frame = tk.Frame(test_frame)
        test_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        # 删除配置测试按钮
        self.test_delete_btn = tk.Button(test_buttons_frame, text="🗑️ 删除配置测试",
                                        command=self.test_delete_configs,
                                        font=("微软雅黑", 10), width=15,
                                        bg="#ff6b6b", fg="white")
        self.test_delete_btn.pack(side=tk.LEFT, padx=5)

        # 创建配置测试按钮
        self.test_create_btn = tk.Button(test_buttons_frame, text="🔧 创建配置测试",
                                        command=self.test_create_configs,
                                        font=("微软雅黑", 10), width=15,
                                        bg="#4ecdc4", fg="white")
        self.test_create_btn.pack(side=tk.LEFT, padx=5)

        # 批量设置测试按钮
        self.test_batch_btn = tk.Button(test_buttons_frame, text="📦 批量设置测试",
                                       command=self.test_batch_submit,
                                       font=("微软雅黑", 10), width=15,
                                       bg="#45b7d1", fg="white")
        self.test_batch_btn.pack(side=tk.LEFT, padx=5)

        # 测试按钮状态说明
        test_status_frame = tk.Frame(test_frame)
        test_status_frame.pack(fill=tk.X, padx=5, pady=2)

        test_help = tk.Label(test_status_frame,
                            text="💡 提示：可以独立执行各个步骤，便于精确定位问题所在",
                            font=("微软雅黑", 8), fg="blue")
        test_help.pack(anchor=tk.W)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
        log_entry = f"{timestamp} {message}\n"

        self.log_text.insert(tk.END, log_entry)

        # 限制日志行数，超过1000行时删除前500行
        line_count = int(self.log_text.index('end-1c').split('.')[0])
        if line_count > 1000:
            self.log_text.delete('1.0', '500.0')

        self.log_text.see(tk.END)
        # 减少GUI更新频率
        if hasattr(self, '_log_update_counter'):
            self._log_update_counter += 1
        else:
            self._log_update_counter = 1

        # 每10条日志更新一次GUI
        if self._log_update_counter % 10 == 0:
            self.root.update_idletasks()

    def on_mode_change(self):
        """处理模式切换"""
        mode = self.processing_mode.get()

        if mode == "limit":
            # 限制模式：启用节点限制编辑框，禁用分批编辑框
            self.limit_entry.config(state="normal")
            self.batch_entry.config(state="disabled")
        else:  # batch
            # 分批模式：禁用节点限制编辑框，启用分批编辑框
            self.limit_entry.config(state="disabled")
            self.batch_entry.config(state="normal")

    def update_progress(self, current, total, message=""):
        """更新进度条"""
        if total > 0:
            percentage = int((current / total) * 100)
            self.progress_bar['value'] = percentage
            
            # 创建进度条显示
            filled = int(percentage / 10)
            empty = 10 - filled
            progress_bar_text = "█" * filled + "░" * empty
            
            if message:
                self.progress_var.set(f"{message}: {progress_bar_text} {percentage}% ({current}/{total})")
            else:
                self.progress_var.set(f"进度: {progress_bar_text} {percentage}% ({current}/{total})")
        else:
            self.progress_var.set("等待开始...")
            self.progress_bar['value'] = 0
            
        self.root.update_idletasks()

    def update_nodes(self):
        """更新节点数据"""
        if self.is_running:
            messagebox.showwarning("操作提示", "批量配置正在进行中，请等待完成后再更新节点！")
            return

        # 禁用更新节点按钮
        self.update_nodes_button.config(state="disabled")

        # 在新线程中执行更新
        update_thread = threading.Thread(target=self.update_nodes_worker)
        update_thread.daemon = True
        update_thread.start()

    def update_nodes_worker(self):
        """更新节点数据工作线程"""
        try:
            self.log_message("🔍 开始更新节点数据...")

            # 使用固定的配置信息
            from config import Config
            host = Config.DEFAULT_HOST
            username = Config.DEFAULT_USERNAME
            password = Config.DEFAULT_PASSWORD

            # 创建客户端
            client = OpenWrtClient(host, username, password)

            # 登录
            self.log_message("🔐 正在登录...")
            if not client.login():
                self.log_message("❌ 登录失败")
                return

            self.log_message("✅ 登录成功")

            # 获取节点数据
            self.log_message("📡 正在获取最新节点数据...")
            nodes = client.get_nodes()
            if not nodes:
                self.log_message("❌ 获取节点数据失败")
                return

            self.log_message(f"✅ 获取到 {len(nodes)} 个节点")

            # 使用智能节点管理功能更新nodes_data.json
            self.log_message("🔄 正在更新节点数据到nodes_data.json...")
            updated_nodes = client.assign_ports_to_nodes(nodes)

            self.log_message(f"🎉 节点数据更新完成！共处理 {len(updated_nodes)} 个可用节点")
            messagebox.showinfo("更新完成", f"节点数据更新成功！\n共处理 {len(updated_nodes)} 个可用节点")

        except Exception as e:
            self.log_message(f"❌ 更新节点数据失败: {e}")
            messagebox.showerror("更新失败", f"更新节点数据时发生错误: {e}")
        finally:
            # 重新启用更新节点按钮
            self.update_nodes_button.config(state="normal")

    def export_socks_config(self):
        """导出socks配置"""
        if self.is_running:
            messagebox.showwarning("操作提示", "批量配置正在进行中，请等待完成后再导出！")
            return

        # 禁用导出按钮
        self.export_button.config(state="disabled")

        # 在新线程中执行导出
        export_thread = threading.Thread(target=self.export_socks_worker)
        export_thread.daemon = True
        export_thread.start()

    def export_socks_worker(self):
        """导出socks配置工作线程"""
        try:
            self.log_message("🔍 开始导出socks配置...")

            # 使用固定的配置信息
            from config import Config
            host = Config.DEFAULT_HOST
            username = Config.DEFAULT_USERNAME
            password = Config.DEFAULT_PASSWORD

            # 创建客户端
            client = OpenWrtClient(host, username, password)

            # 登录
            self.log_message("🔐 正在登录...")
            if not client.login():
                self.log_message("❌ 登录失败")
                return

            self.log_message("✅ 登录成功")

            # 获取节点数据
            self.log_message("📡 正在获取节点数据...")
            nodes = client.get_nodes()
            if not nodes:
                self.log_message("❌ 获取节点数据失败")
                return

            self.log_message(f"✅ 获取到 {len(nodes)} 个节点")

            # 获取当前socks配置
            self.log_message("🔍 正在获取当前socks配置...")
            token, existing_socks = client.get_token_and_socks_config()
            if not token:
                self.log_message("❌ 获取token失败")
                return

            self.log_message(f"✅ 获取到 {len(existing_socks)} 个现有socks配置")

            # 生成socks配置列表
            socks_list = []
            for i, config_id in enumerate(existing_socks):
                if i < len(nodes):
                    node = nodes[i]
                    port = 10001 + i
                    socks_url = f"socks5://{host.replace('http://', '').replace('https://', '')}:{port}{{{node['name']}}}"
                    socks_list.append(socks_url)

            # 显示在文本框中
            self.socks_text.delete(1.0, tk.END)
            if socks_list:
                socks_content = "\n".join(socks_list)
                self.socks_text.insert(tk.END, socks_content)
                self.log_message(f"✅ 导出完成！共 {len(socks_list)} 个socks配置")
            else:
                self.socks_text.insert(tk.END, "暂无socks配置")
                self.log_message("⚠️ 暂无socks配置可导出")

        except Exception as e:
            self.log_message(f"❌ 导出socks配置失败: {e}")
        finally:
            # 重新启用导出按钮
            self.export_button.config(state="normal")

    def validate_inputs(self):
        """验证输入参数"""
        # 现在使用固定配置，无需验证输入
        from config import Config

        # 验证配置文件中的参数
        if not Config.DEFAULT_HOST:
            messagebox.showerror("配置错误", "配置文件中缺少OpenWrt地址！")
            return False

        if not Config.DEFAULT_USERNAME:
            messagebox.showerror("配置错误", "配置文件中缺少用户名！")
            return False

        if not Config.DEFAULT_PASSWORD:
            messagebox.showerror("配置错误", "配置文件中缺少密码！")
            return False

        return True

    def is_valid_host(self, host):
        """验证主机地址格式"""
        import re
        # 简单的IP地址或域名验证
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'

        return re.match(ip_pattern, host) or re.match(domain_pattern, host)

    def start_batch_config(self):
        """开始批量配置"""
        if self.is_running:
            messagebox.showwarning("警告", "配置正在进行中，请等待完成！")
            return

        # 验证输入
        if not self.validate_inputs():
            return

        # 使用固定配置参数
        from config import Config
        host = Config.DEFAULT_HOST
        username = Config.DEFAULT_USERNAME
        password = Config.DEFAULT_PASSWORD

        # 直接开始执行，不显示确认对话框

        # 禁用开始按钮并更改文本
        self.start_button.config(state=tk.DISABLED, text="配置中...", bg="#FF9800")
        self.is_running = True

        # 清空日志
        self.log_text.delete(1.0, tk.END)
        request_logger.clear_log()  # 清空文件日志
        self.update_progress(0, 0)

        # 获取4个独立的调试文件保存选项
        save_create = self.save_create_files.get()
        save_delete = self.save_delete_files.get()
        save_token = self.save_token_files.get()
        save_batch = self.save_batch_files.get()

        # 获取处理模式和相关参数
        processing_mode = self.processing_mode.get()

        if processing_mode == "limit":
            # 限制模式：获取节点数量限制
            try:
                node_limit = int(self.node_limit_var.get())
                if node_limit < 0:
                    node_limit = 0  # 负数转为0
            except ValueError:
                node_limit = 0  # 无效输入转为0
            batch_size = 0  # 分批大小设为0表示不分批
        else:  # batch
            # 分批模式：获取每批数量
            try:
                batch_size = int(self.batch_size_var.get())
                if batch_size <= 0:
                    batch_size = 40  # 无效输入转为默认值40
            except ValueError:
                batch_size = 40  # 无效输入转为默认值40
            node_limit = 0  # 节点限制设为0表示处理所有节点

        # 在新线程中执行配置
        thread = threading.Thread(target=self.batch_config_worker,
                                 args=(host, username, password, save_create, save_delete, save_token, save_batch, processing_mode, node_limit, batch_size))
        thread.daemon = True
        thread.start()

    def batch_config_worker(self, host, username, password, save_create, save_delete, save_token, save_batch, processing_mode, node_limit, batch_size):
        """批量配置工作线程"""
        success = False
        try:
            # 显示用户选择状态
            retry_status = "启用" if self.enable_retry_login.get() else "禁用"

            if processing_mode == "limit":
                mode_status = f"限制模式（处理前{node_limit}个节点）" if node_limit > 0 else "限制模式（处理所有节点）"
            else:  # batch
                mode_status = f"分批模式（每批{batch_size}个节点）"

            self.log_message(f"🔧 用户配置：二次登录重新提交 - {retry_status}")
            self.log_message(f"🔧 用户配置：调试文件保存 - 创建包:{save_create}, 删除包:{save_delete}, Token页面:{save_token}, 批量提交包:{save_batch}")
            self.log_message(f"🔧 用户配置：处理模式 - {mode_status}")

            # 初始化客户端
            self.log_message("📱 初始化连接...")
            self.client = OpenWrtClient(host, username, password)

            # 设置4个独立的调试文件保存选项
            self.client.save_create_files = save_create
            self.client.save_delete_files = save_delete
            self.client.save_token_files = save_token
            self.client.save_batch_files = save_batch

            # 步骤1: 登录
            self.log_message("开始登录...")
            if not self.client.login():
                self.log_message("❌ 登录失败！请检查地址、用户名和密码")
                self.show_error_message("登录失败", "请检查OpenWrt地址、用户名和密码是否正确")
                success = False
                return
            self.log_message("✅ 登录成功")

            # 步骤2: 获取节点列表
            self.log_message("获取节点列表...")
            nodes = self.client.get_nodes()
            if not nodes:
                self.log_message("❌ 未找到任何节点")
                self.show_error_message("节点获取失败", "未找到任何可用节点，请检查Passwall2配置")
                success = False
                return
            self.log_message(f"✅ 找到{len(nodes)}个节点")

            # 根据处理模式应用不同的逻辑
            original_count = len(nodes)

            if processing_mode == "limit":
                # 限制模式：只处理前N个节点
                if node_limit > 0 and node_limit < len(nodes):
                    nodes = nodes[:node_limit]
                    self.log_message(f"🔢 限制模式：从{original_count}个节点中选择前{node_limit}个")
                else:
                    self.log_message(f"🔢 限制模式：处理所有{original_count}个节点")

                # 显示要处理的节点信息
                self.log_message(f"📋 将要处理的{len(nodes)}个节点：")
                for i, node in enumerate(nodes, 1):
                    node_info = f"  {i}. {node['name']} ({node['region']})"
                    if 'type' in node:
                        node_info += f" [{node['type']}]"
                    if 'address' in node and 'port' in node:
                        node_info += f" {node['address']}:{node['port']}"
                    self.log_message(node_info)

                # 处理单批节点
                success = self.process_single_batch(nodes, 1, 1, skip_deletion=False, processing_mode="limit", node_limit=node_limit)

            else:  # batch mode
                # 分批模式：将所有节点分批处理
                self.log_message(f"🔢 分批模式：将{original_count}个节点分批处理，每批{batch_size}个")

                # 计算总批数
                total_batches = (original_count + batch_size - 1) // batch_size
                self.log_message(f"📊 总共需要处理{total_batches}批")

                # 🗑️ 分批模式：统一删除所有现有配置（只执行一次）
                self.log_message("🗑️ 分批模式：统一删除所有现有配置...")
                token, existing_socks = self.client.get_token_and_socks_config()

                if not token:
                    self.log_message("❌ 获取token失败")
                    self.show_error_message("Token获取失败", "无法获取必要的token，请检查登录状态")
                    success = False
                    return

                # 🔧 重要：将获取到的token赋值给client对象
                self.client.token = token
                self.log_message(f"🔑 已更新client token: {token}")

                if existing_socks:
                    self.log_message(f"开始删除{len(existing_socks)}个现有Socks配置...")
                    if self.client.delete_existing_socks(existing_socks):
                        self.log_message("✅ 统一删除现有配置成功")
                    else:
                        self.log_message("❌ 统一删除现有配置失败")
                        self.show_error_message("删除失败", "删除现有Socks配置失败，请手动检查")
                        success = False
                        return
                else:
                    self.log_message("无现有Socks配置需要删除")

                # 分批处理所有节点（各批次跳过删除步骤）
                self.log_message("🚀 开始分批创建配置（各批次将跳过删除步骤）...")
                success = self.process_all_batches(nodes, batch_size, total_batches)

        except Exception as e:
            self.log_message(f"❌ 批量配置过程中发生异常: {e}")
            self.show_error_message("配置失败", f"批量配置过程中发生异常: {e}")
            success = False
        finally:
            # 恢复开始按钮状态
            self.start_button.config(state=tk.NORMAL, text="开始批量配置", bg="#4CAF50")
            self.is_running = False

            # 显示最终结果
            if success:
                self.log_message("🎉 批量配置完成！")
                messagebox.showinfo("成功", "批量配置已完成！")
            else:
                self.log_message("❌ 批量配置失败")

    def process_single_batch(self, nodes, batch_num, total_batches, skip_deletion=False, processing_mode="limit", node_limit=0):
        """处理单批节点（限制模式或分批模式的单批）"""
        try:
            if not skip_deletion:
                # 步骤3: 获取token和已存在的socks配置
                self.log_message("获取token和socks配置...")
                token, existing_socks = self.client.get_token_and_socks_config()

                if not token:
                    self.log_message("❌ 获取token失败")
                    self.show_error_message("Token获取失败", "无法获取必要的token，请检查登录状态")
                    return False

                # 🔧 重要：将获取到的token赋值给client对象
                self.client.token = token
                self.log_message(f"🔑 已更新client token: {token}")

                self.log_message(f"✅ 找到{len(existing_socks)}个现有Socks配置")

                # 步骤4: 删除现有配置
                if existing_socks:
                    self.log_message("开始删除现有Socks配置...")
                    if self.client.delete_existing_socks(existing_socks):
                        self.log_message("✅ 删除现有配置成功")
                    else:
                        self.log_message("❌ 删除现有配置失败")
                        self.show_error_message("删除失败", "删除现有Socks配置失败，请手动检查")
                        return False
                else:
                    self.log_message("无现有Socks配置需要删除")
            else:
                # 跳过删除步骤，但仍需要获取token
                self.log_message("🔄 获取token（跳过删除现有配置）...")
                token, _ = self.client.get_token_and_socks_config()

                if not token:
                    self.log_message("❌ 获取token失败")
                    self.show_error_message("Token获取失败", "无法获取必要的token，请检查登录状态")
                    return False

                # 🔧 重要：将获取到的token赋值给client对象
                self.client.token = token
                self.log_message(f"🔑 已更新client token: {token}")
                self.log_message("⏭️ 跳过删除现有配置步骤（分批模式）")

            # 步骤5: 为所有节点分配端口
            self.log_message("开始为节点分配端口...")
            nodes = self.client.assign_ports_to_nodes(nodes)
            self.log_message(f"✅ 端口分配完成，共 {len(nodes)} 个节点")

            # 步骤6: 创建所有节点的Socks配置
            self.log_message(f"🚀 开始创建所有 {len(nodes)} 个节点的Socks配置")

            if len(nodes) > 0:
                # 获取目标配置数量
                if processing_mode == "limit":
                    # 限制模式：使用节点限制数量
                    target_count = node_limit if node_limit > 0 else len(nodes)
                else:
                    # 分批模式：使用当前批次的节点数量
                    target_count = len(nodes)

                # 第一阶段：使用新的实时计数创建配置
                self.log_message("📋 第一阶段：创建空配置（实时计数控制）...")
                config_ids, final_nodes, ports = self.create_configs_with_count_control(nodes, target_count)

                # 第二阶段：批量提交配置参数
                if len(config_ids) >= target_count:
                    self.log_message(f"🚀 第二阶段：批量提交 {target_count} 个配置的参数...")
                    # 批量提交阶段开始，进度设为70%
                    progress_value = int(target_count * 0.7)
                    self.update_progress(progress_value, target_count, "开始批量提交配置参数")

                    # 根据用户选择决定是否启用二次登录重新提交
                    enable_retry = self.enable_retry_login.get()
                    if enable_retry:
                        self.log_message("🔄 启用二次登录重新提交模式")
                    else:
                        self.log_message("🔄 使用单次提交模式")

                    # 检查是否启用网页token
                    web_token_data = None
                    if self.enable_web_token.get():
                        self.log_message("🌐 启用网页token模式，请输入token信息...")
                        dialog = TokenInputDialog(self.root)
                        web_token_data = dialog.show_dialog()

                        if web_token_data:
                            self.log_message(f"✅ 获取到网页token: sessionid={web_token_data['sessionid'][:8]}..., token={web_token_data['token'][:8]}...")
                        else:
                            self.log_message("⚠️ 用户取消了网页token输入，将使用自动获取的token")

                    try:
                        if self.client.batch_submit_all_configs(config_ids, final_nodes, ports, enable_retry, web_token_data):
                            self.log_message(f"🎉 批量提交成功！所有 {target_count} 个配置参数已设置")
                            # 完成时进度设为100%
                            self.update_progress(target_count, target_count, "批量配置完成")
                            return True
                        else:
                            self.log_message("❌ 批量提交失败")
                            self.show_error_message("批量提交失败", "设置配置参数时失败")
                            return False
                    except Exception as e:
                        self.log_message(f"❌ 批量提交异常: {e}")
                        self.show_error_message("批量提交异常", f"批量提交时发生异常: {e}")
                        return False
                else:
                    self.log_message(f"❌ 空配置创建不完整，只成功创建了 {len(config_ids)}/{target_count} 个")
                    return False
            else:
                self.log_message("❌ 没有可用节点，无法创建配置")
                return False

        except Exception as e:
            self.log_message(f"❌ 处理批次时发生异常: {e}")
            return False

    def create_configs_with_count_control(self, nodes, target_count):
        """根据配置数量控制创建过程（正确逻辑：创建配置直到数量够，然后对应前N个节点）"""
        try:
            self.log_message(f"🎯 开始创建配置，目标数量: {target_count}")

            # 获取初始配置数量
            initial_configs = self.client.get_current_socks_configs()
            initial_count = len(initial_configs)
            self.log_message(f"📊 初始配置数量: {initial_count}")

            # 第一步：创建配置直到数量够
            node_index = 0
            while True:
                # 获取当前配置数量
                current_configs = self.client.get_current_socks_configs()
                current_count = len(current_configs) - initial_count

                self.log_message(f"� 当前配置数量: {current_count}/{target_count}")

                # 检查是否达到目标数量
                if current_count >= target_count:
                    self.log_message(f"🎯 已达到目标配置数量 {target_count}，停止创建")
                    break

                # 检查是否还有节点可用
                if node_index >= len(nodes):
                    self.log_message(f"⚠️ 节点已用完，但配置数量不足")
                    break

                # 创建下一个节点的配置
                node = nodes[node_index]
                self.log_message(f"🔄 创建配置: {node['name']} ({node['region']})")

                try:
                    # 创建单个配置（不保存配置ID）
                    config_id = self.client.create_empty_socks_config(node)

                    # 更新进度条
                    progress_value = int(min(current_count + 1, target_count) / target_count * 70)
                    self.update_progress(progress_value, 100, f"配置创建中 {min(current_count + 1, target_count)}/{target_count}")

                except Exception as e:
                    self.log_message(f"❌ 创建异常: {node['name']} - {e}")

                node_index += 1

            # 第二步：获取所有配置ID和前N个节点，一一对应
            final_configs = self.client.get_current_socks_configs()
            final_count = len(final_configs) - initial_count

            if final_count >= target_count:
                # 获取配置ID（取最新的target_count个）
                new_config_ids = final_configs[initial_count:initial_count + target_count]

                # 获取前target_count个节点
                target_nodes = nodes[:target_count]

                # 一一对应
                final_nodes = []
                final_ports = []

                self.log_message(f"🔍 配置ID与节点对应关系：")
                for i in range(target_count):
                    config_id = new_config_ids[i]
                    node = target_nodes[i]

                    final_nodes.append(node)
                    final_ports.append(node['socks_port'])

                    self.log_message(f"  配置 {i+1}: {config_id} → {node['name']} (端口: {node['socks_port']})")

                self.log_message(f"🎉 配置创建完成！创建了 {final_count} 个配置，使用前 {target_count} 个对应 {target_count} 个节点")

                return new_config_ids, final_nodes, final_ports
            else:
                self.log_message(f"❌ 配置创建不足，目标 {target_count} 个，实际只创建了 {final_count} 个")
                return [], [], []

        except Exception as e:
            self.log_message(f"❌ 创建配置过程中发生异常: {e}")
            return [], [], []

    def process_all_batches(self, nodes, batch_size, total_batches):
        """分批处理所有节点"""
        try:
            all_success = True

            for batch_num in range(total_batches):
                # 计算当前批次的节点范围
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, len(nodes))
                batch_nodes = nodes[start_idx:end_idx]

                self.log_message(f"🔢 分批处理：第{batch_num + 1}批，处理节点{start_idx + 1}-{end_idx}（共{len(batch_nodes)}个）")

                # 显示当前批次的节点信息
                self.log_message(f"📋 第{batch_num + 1}批将要处理的{len(batch_nodes)}个节点：")
                for i, node in enumerate(batch_nodes, start_idx + 1):
                    node_info = f"  {i}. {node['name']} ({node['region']})"
                    if 'type' in node:
                        node_info += f" [{node['type']}]"
                    if 'address' in node and 'port' in node:
                        node_info += f" {node['address']}:{node['port']}"
                    self.log_message(node_info)

                # 处理当前批次（跳过删除步骤）
                batch_success = self.process_single_batch(batch_nodes, batch_num + 1, total_batches, skip_deletion=True, processing_mode="batch", node_limit=0)

                if batch_success:
                    self.log_message(f"✅ 第{batch_num + 1}批处理完成！")
                else:
                    self.log_message(f"❌ 第{batch_num + 1}批处理失败")
                    all_success = False
                    break

                # 如果不是最后一批，稍作延迟
                if batch_num < total_batches - 1:
                    self.log_message(f"⏳ 准备处理下一批...")
                    import time
                    time.sleep(2)  # 2秒延迟

            if all_success:
                self.log_message(f"🎉 所有分批处理完成！总共处理了{len(nodes)}个节点，分{total_batches}批完成")

            return all_success

        except Exception as e:
            self.log_message(f"❌ 分批处理过程中发生异常: {e}")
            return False
            #         self.log_message(f"✅ 节点 {node['name']} 配置成功")
            #         success_count += 1
            #     else:
            #         self.log_message(f"❌ 节点 {node['name']} 配置失败")
            #         self.show_error_message("配置失败", f"节点 {node['name']} 配置失败，流程已停止")
            #         return

            #     time.sleep(0.5)  # 避免请求过快

            # self.update_progress(total_nodes, total_nodes, "配置完成")
            # self.log_message(f"🎉 所有Socks配置创建完成！成功配置 {success_count}/{total_nodes} 个节点")
            # success = True



    def show_error_message(self, title, message):
        """显示错误消息"""
        def show_msg():
            messagebox.showerror(title, message)
        self.root.after(0, show_msg)

    def show_success_message(self, title, message):
        """显示成功消息"""
        def show_msg():
            messagebox.showinfo(title, message)
        self.root.after(0, show_msg)

    # ==================== 测试功能方法 ====================

    def test_delete_configs(self):
        """测试删除配置功能"""
        self.log_message("=" * 50)
        self.log_message("🗑️ 开始删除配置测试")
        self.log_message("=" * 50)

        # 禁用测试按钮
        self.set_test_buttons_state(False)

        # 在新线程中执行删除测试
        thread = threading.Thread(target=self.delete_test_worker)
        thread.daemon = True
        thread.start()

    def test_create_configs(self):
        """测试创建配置功能"""
        self.log_message("=" * 50)
        self.log_message("🔧 开始创建配置测试")
        self.log_message("=" * 50)

        # 禁用测试按钮
        self.set_test_buttons_state(False)

        # 在新线程中执行创建测试
        thread = threading.Thread(target=self.create_test_worker)
        thread.daemon = True
        thread.start()

    def test_batch_submit(self):
        """测试批量设置功能"""
        self.log_message("=" * 50)
        self.log_message("📦 开始批量设置测试")
        self.log_message("=" * 50)

        # 禁用测试按钮
        self.set_test_buttons_state(False)

        # 在新线程中执行批量设置测试
        thread = threading.Thread(target=self.batch_test_worker)
        thread.daemon = True
        thread.start()

    def set_test_buttons_state(self, enabled):
        """设置测试按钮的启用/禁用状态"""
        state = "normal" if enabled else "disabled"
        self.test_delete_btn.config(state=state)
        self.test_create_btn.config(state=state)
        self.test_batch_btn.config(state=state)

    def delete_test_worker(self):
        """删除配置测试工作线程"""
        success = False
        try:
            # 使用固定的配置信息
            from config import Config
            host = Config.DEFAULT_HOST
            username = Config.DEFAULT_USERNAME
            password = Config.DEFAULT_PASSWORD

            # 步骤1: 创建客户端并登录
            self.log_message("🔑 步骤1: 登录OpenWrt系统...")
            self.client = OpenWrtClient(host, username, password)

            # 设置调试文件保存选项（测试时使用用户选择）
            self.client.save_create_files = self.save_create_files.get()
            self.client.save_delete_files = self.save_delete_files.get()
            self.client.save_token_files = self.save_token_files.get()
            self.client.save_batch_files = self.save_batch_files.get()

            if not self.client.login():
                self.log_message("❌ 登录失败")
                self.show_error_message("登录失败", "无法登录到OpenWrt系统，请检查连接信息")
                return

            self.log_message("✅ 登录成功")

            # 步骤2: 获取token和现有配置
            self.log_message("🔑 步骤2: 获取token和现有Socks配置...")
            token, existing_socks = self.client.get_token_and_socks_config()

            if not token:
                self.log_message("❌ 获取token失败")
                self.show_error_message("Token获取失败", "无法获取必要的token，请检查登录状态")
                return

            self.client.token = token
            self.log_message(f"✅ 获取token成功: {token}")
            self.log_message(f"📋 找到 {len(existing_socks)} 个现有Socks配置")

            # 步骤3: 删除现有配置
            if existing_socks:
                self.log_message("🗑️ 步骤3: 删除现有Socks配置...")
                for i, config in enumerate(existing_socks, 1):
                    self.log_message(f"  {i}. {config}")

                if self.client.delete_existing_socks(existing_socks):
                    self.log_message("✅ 删除现有配置成功")
                    success = True
                else:
                    self.log_message("❌ 删除现有配置失败")
                    self.show_error_message("删除失败", "删除现有Socks配置失败")
            else:
                self.log_message("📋 无现有Socks配置需要删除")
                success = True

        except Exception as e:
            self.log_message(f"❌ 删除配置测试过程中发生异常: {e}")
            self.show_error_message("测试异常", f"删除配置测试时发生异常: {e}")

        finally:
            # 恢复按钮状态
            self.set_test_buttons_state(True)

            # 显示测试结果
            if success:
                self.log_message("🎉 删除配置测试完成！")
                self.show_success_message("测试完成", "删除配置测试成功完成！")
            else:
                self.log_message("❌ 删除配置测试失败")

            self.log_message("=" * 50)

    def create_test_worker(self):
        """创建配置测试工作线程"""
        success = False
        try:
            # 使用固定的配置信息
            from config import Config
            host = Config.DEFAULT_HOST
            username = Config.DEFAULT_USERNAME
            password = Config.DEFAULT_PASSWORD

            # 获取处理模式和相关参数
            processing_mode = self.processing_mode.get()

            if processing_mode == "limit":
                try:
                    node_limit = int(self.node_limit_var.get())
                    if node_limit < 0:
                        node_limit = 0
                except ValueError:
                    node_limit = 0
                batch_size = 40  # 默认值
            else:  # batch
                try:
                    batch_size = int(self.batch_size_var.get())
                    if batch_size <= 0:
                        batch_size = 40
                except ValueError:
                    batch_size = 40
                node_limit = 0  # 默认值

            # 显示测试配置
            if processing_mode == "limit":
                mode_status = f"限制模式（处理前{node_limit}个节点）" if node_limit > 0 else "限制模式（处理所有节点）"
            else:
                mode_status = f"分批模式（每批{batch_size}个节点）"

            self.log_message(f"🔧 测试配置：{mode_status}")

            # 步骤1: 创建客户端并登录
            self.log_message("🔑 步骤1: 登录OpenWrt系统...")
            self.client = OpenWrtClient(host, username, password)

            # 设置调试文件保存选项（测试时使用用户选择）
            self.client.save_create_files = self.save_create_files.get()
            self.client.save_delete_files = self.save_delete_files.get()
            self.client.save_token_files = self.save_token_files.get()
            self.client.save_batch_files = self.save_batch_files.get()

            if not self.client.login():
                self.log_message("❌ 登录失败")
                self.show_error_message("登录失败", "无法登录到OpenWrt系统，请检查连接信息")
                return

            self.log_message("✅ 登录成功")

            # 步骤2: 获取节点列表
            self.log_message("📡 步骤2: 获取节点列表...")
            nodes = self.client.get_nodes()

            if not nodes:
                self.log_message("❌ 获取节点失败")
                self.show_error_message("节点获取失败", "无法获取节点列表，请检查网络连接")
                return

            original_count = len(nodes)
            self.log_message(f"✅ 成功获取 {original_count} 个节点")

            # 步骤3: 应用处理模式限制
            if processing_mode == "limit":
                if node_limit > 0 and node_limit < len(nodes):
                    nodes = nodes[:node_limit]
                    self.log_message(f"🔢 限制模式：从{original_count}个节点中选择前{node_limit}个")
                else:
                    self.log_message(f"🔢 限制模式：处理所有{original_count}个节点")
            else:
                # 分批模式：只处理第一批进行测试
                if len(nodes) > batch_size:
                    nodes = nodes[:batch_size]
                    self.log_message(f"🔢 分批模式测试：只处理第一批{batch_size}个节点（共{original_count}个）")
                else:
                    self.log_message(f"🔢 分批模式测试：处理所有{original_count}个节点")

            # 显示要处理的节点信息
            self.log_message(f"📋 将要创建的{len(nodes)}个节点配置：")
            for i, node in enumerate(nodes[:5], 1):  # 只显示前5个
                node_info = f"  {i}. {node['name']} ({node['region']})"
                if 'address' in node and 'port' in node:
                    node_info += f" {node['address']}:{node['port']}"
                self.log_message(node_info)

            if len(nodes) > 5:
                self.log_message(f"  ... 还有 {len(nodes) - 5} 个节点")

            # 步骤4: 为节点分配端口
            self.log_message("🔌 步骤4: 为节点分配端口...")
            nodes = self.client.assign_ports_to_nodes(nodes)
            self.log_message(f"✅ 端口分配完成，共 {len(nodes)} 个节点")

            # 步骤5: 获取token
            self.log_message("🔑 步骤5: 获取token...")
            token, _ = self.client.get_token_and_socks_config()

            if not token:
                self.log_message("❌ 获取token失败")
                self.show_error_message("Token获取失败", "无法获取必要的token")
                return

            self.client.token = token
            self.log_message(f"✅ 获取token成功: {token}")

            # 步骤6: 使用新的实时计数创建配置
            target_count = len(nodes)
            self.log_message(f"� 步骤6: 开始创建 {target_count} 个空配置（实时计数控制）...")

            config_ids, final_nodes, ports = self.create_configs_with_count_control(nodes, target_count)

            # 检查创建结果
            if len(config_ids) >= target_count:
                self.log_message(f"🎉 配置创建测试成功！目标 {target_count} 个，实际获得 {len(config_ids)} 个")
                self.log_message("📋 创建的配置ID列表：")
                for i, (config_id, node) in enumerate(zip(config_ids[:target_count], final_nodes[:target_count]), 1):
                    self.log_message(f"  {i}. {config_id} -> {node['name']}")
                success = True
            else:
                self.log_message(f"❌ 配置创建测试失败，目标 {target_count} 个，实际只获得 {len(config_ids)} 个")

        except Exception as e:
            self.log_message(f"❌ 创建配置测试过程中发生异常: {e}")
            self.show_error_message("测试异常", f"创建配置测试时发生异常: {e}")

        finally:
            # 恢复按钮状态
            self.set_test_buttons_state(True)

            # 显示测试结果
            if success:
                self.log_message("🎉 创建配置测试完成！")
                self.show_success_message("测试完成", "创建配置测试成功完成！")
            else:
                self.log_message("❌ 创建配置测试失败")

            self.log_message("=" * 50)

    def batch_test_worker(self):
        """批量设置测试工作线程"""
        success = False
        try:
            # 使用固定的配置信息
            from config import Config
            host = Config.DEFAULT_HOST
            username = Config.DEFAULT_USERNAME
            password = Config.DEFAULT_PASSWORD

            # 步骤1: 创建客户端并登录
            self.log_message("🔑 步骤1: 登录OpenWrt系统...")
            self.client = OpenWrtClient(host, username, password)

            # 设置调试文件保存选项（测试时使用用户选择）
            self.client.save_create_files = self.save_create_files.get()
            self.client.save_delete_files = self.save_delete_files.get()
            self.client.save_token_files = self.save_token_files.get()
            self.client.save_batch_files = self.save_batch_files.get()

            if not self.client.login():
                self.log_message("❌ 登录失败")
                self.show_error_message("登录失败", "无法登录到OpenWrt系统，请检查连接信息")
                return

            self.log_message("✅ 登录成功")

            # 步骤2: 获取token和现有配置
            self.log_message("🔑 步骤2: 获取token和现有Socks配置...")
            token, existing_socks = self.client.get_token_and_socks_config()

            if not token:
                self.log_message("❌ 获取token失败")
                self.show_error_message("Token获取失败", "无法获取必要的token")
                return

            self.client.token = token
            self.log_message(f"✅ 获取token成功: {token}")

            # 检查是否有现有配置
            if not existing_socks:
                self.log_message("❌ 未找到现有Socks配置")
                self.log_message("💡 提示：请先运行'创建配置测试'创建一些配置，然后再测试批量设置")
                self.show_error_message("无配置可设置", "未找到现有Socks配置，请先创建一些配置")
                return

            self.log_message(f"📋 找到 {len(existing_socks)} 个现有Socks配置")
            for i, config in enumerate(existing_socks, 1):
                self.log_message(f"  {i}. {config}")

            # 步骤3: 获取节点列表（用于批量设置）
            self.log_message("📡 步骤3: 获取节点列表...")
            nodes = self.client.get_nodes()

            if not nodes:
                self.log_message("❌ 获取节点失败")
                self.show_error_message("节点获取失败", "无法获取节点列表")
                return

            # 限制节点数量与现有配置数量匹配
            if len(nodes) > len(existing_socks):
                nodes = nodes[:len(existing_socks)]
                self.log_message(f"🔢 调整节点数量：使用前{len(existing_socks)}个节点匹配现有配置")

            self.log_message(f"✅ 将使用 {len(nodes)} 个节点进行批量设置")

            # 步骤4: 为节点分配端口
            self.log_message("🔌 步骤4: 为节点分配端口...")
            nodes = self.client.assign_ports_to_nodes(nodes)
            ports = [node['socks_port'] for node in nodes]
            self.log_message(f"✅ 端口分配完成")

            # 步骤5: 批量提交配置参数
            self.log_message("📦 步骤5: 开始批量提交配置参数...")

            # 获取用户的二次登录重新提交选择
            enable_retry = self.enable_retry_login.get()
            retry_status = "启用" if enable_retry else "禁用"
            self.log_message(f"🔧 二次登录重新提交: {retry_status}")

            # 检查是否启用网页token
            web_token_data = None
            if self.enable_web_token.get():
                self.log_message("🌐 启用网页token模式，请输入token信息...")
                dialog = TokenInputDialog(self.root)
                web_token_data = dialog.show_dialog()

                if web_token_data:
                    self.log_message(f"✅ 获取到网页token: sessionid={web_token_data['sessionid'][:8]}..., token={web_token_data['token'][:8]}...")
                else:
                    self.log_message("⚠️ 用户取消了网页token输入，将使用自动获取的token")

            # 执行批量提交
            if self.client.batch_submit_all_configs(existing_socks, nodes, ports, enable_retry, web_token_data):
                self.log_message("🎉 批量提交成功！")
                self.log_message("📋 批量设置详情：")
                for i, (config_id, node, port) in enumerate(zip(existing_socks, nodes, ports), 1):
                    self.log_message(f"  {i}. 配置 {config_id} -> 节点 {node['name']} -> 端口 {port}")
                success = True
            else:
                self.log_message("❌ 批量提交失败")
                self.show_error_message("批量提交失败", "设置配置参数时失败")

        except Exception as e:
            self.log_message(f"❌ 批量设置测试过程中发生异常: {e}")
            self.show_error_message("测试异常", f"批量设置测试时发生异常: {e}")

        finally:
            # 恢复按钮状态
            self.set_test_buttons_state(True)

            # 显示测试结果
            if success:
                self.log_message("🎉 批量设置测试完成！")
                self.show_success_message("测试完成", "批量设置测试成功完成！")
            else:
                self.log_message("❌ 批量设置测试失败")

            self.log_message("=" * 50)

def main():
    root = tk.Tk()
    app = PasswallSocksApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
