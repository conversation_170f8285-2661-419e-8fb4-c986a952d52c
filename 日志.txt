OpenWrt Passwall 工具运行日志
==================================================

[2025-08-04 13:40:42] [INFO] 开始登录OpenWrt后台

[2025-08-04 13:40:42] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 13:40:42] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=eed132fe410860b4084464fac4472488; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=eed132fe410860b4084464fac4472488; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: eed132fe410860b4084464fac4472488

响应内容:

==================================================

[2025-08-04 13:40:42] [SUCCESS] ✅ 登录成功！Session ID: eed132fe410860b4084464fac4472488

[2025-08-04 13:40:42] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:40:44] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-04 13:40:44] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点

[2025-08-04 13:40:45] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:40:46] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-04 13:40:46] [SUCCESS] 🔑 成功提取token: a046354b02db006f693b455e7cd395c3
[2025-08-04 13:40:46] [SUCCESS] 📋 已存在的socks配置: ['cR5e5hQl', '6aYXmSCW', 'Hig3Gul8', '1ZYg66EF', 'Kx3wjzoT', 'iKT55Hxu', '0k9OKsYx', 'MuPL8vdY', 'O7GigvWZ', 'X2dnzMMu', 'MCA0Yu1W', 'FImNO6Mi', 'HXZxMhP4', 'cv1Q1hug', '0HjjletM', 'ylSbVfQr', 'pv5jQWHZ', 'LFF9X6ys', 'zQkYm6Og', '0blt6fpv', 'zfuJRAKd', 'EdzvfE5O', 'xyI5Qg28', '2IEgrUqa', '6gxQOTci', 'gMsjBZ33', 'Ee9Q2lsw', 'SKm7zPeG', '8E3tOtXO', 'wV5KQsae', 'ICu4d8Wp', 'E8rjx8XY', '5CgMpKxq', 'YnxK3At5', '6zAecDa3', 'F323ODAd', 'twblhRBJ', 'e6mSkeDA', 'm5qOBJln', 'hZwvb2Je', 'nXwGgZmh', 'gfFdDUHC', 'ggFpijwy', 'FU7un6jQ', 'LpZo9jn0', '4bMiLI6G', 'DeZNVzuq', 'ekbmZ603', 'WYfk5KmH', 'YzzWKey4', 'yNncNCp5', 'MTpnztNa', '7dNbMq2H', 'VJ0hLII7', 'EICJz4RA', '78b2OTLz', '9T1Kco4C', 'iMfSvppi', '21tY8iH5', 'BWZz040S', 'KJm1wh2o', '8Bl7KbFY', 'py7OGByI', 'yMlwBLOv', 'uLlTAPzy', '335D3uuC', 'p5WuLx2R', 'OJKzulGT', 'dnyWdx0O', 'z0RzV5Wp', 'p1TOKHtI', 'JLADaHFj', 'eIZXmAuj', 'qR30tk65', 'bOW3cCOU', 'Dk5qTWaw', 'tqQ1tOrH', '25R1ogiI', 'XRqvTxdX', 'jrCT7kAI', '6ECz7zSR', 'HZPYgo87', 'BimEDiFX', 'sFNywk3K', 'm51NpsGM', 'pAI2ff3j', 'xsxjM44R', 'yUO5lPR4', 'MLHLfzv5', 'fF5bta9g', 'vFr3c0fB', '2qvijW0I', 'S1G0LmMN', '29fxGHYF', 'm8vFmJG0', 'KxCCdngQ', 'NLxux6n2', 'AuxJRVdf', 'LfNNeos6', 'imbT9yrD', 'Pkk3OzBY', 'XdiyZjbX', 'ml7rcZUv', 'rLPZdZFy', 'JmBzIPt4', 'XSPKt7Tb', 'xbohEX8d', 'k5jk84Zd', 'aFOWQscq', 'nWC1yOkE', '3jpw6g6B', 'LqzsrSIL', 'FPeptSiQ', 'C3MjALQ8', 'DyorE7sS', 'Xf5i2N4h', 'PmeIWdsk', 'MGGSXEpU', 'KoJKnlfg', '2V3pK5O0', 'msVSdPf9', 'c1O3T19J', 'CXq02MyG', 'fDkcoJMp', 'gcdn4Xtj', 'qeMNd2V1', 'VjAnZ6LJ', '3ITGKIzA', 'JkDluXqo', '6saV1FOQ', 'wERBRmSL', 'ALP7TyBx', 'LIdAbA1v', 'C5qyPGTp', 'EiwPwz5h', 'V6Q37ikC']

[2025-08-04 13:40:46] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
  Accept-Encoding: gzip, deflate
  Accept-Language: zh-CN,zh;q=0.9
  Cache-Control: max-age=0
  Origin: http://*************
  Referer: http://*************/cgi-bin/luci/admin/services/passwall2/settings
  Upgrade-Insecure-Requests: 1
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryACE5azrrYYWFcSp0
POST数据: multipart_data
------------------------------
[2025-08-04 13:40:46] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - åºæ¬è®¾ç½®
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-tou...
==================================================

[2025-08-04 13:40:46] [SUCCESS] ✅ 删除socks配置请求成功
[2025-08-04 13:40:46] [INFO] 💾 已更新nodes_data.json，共 135 个节点
[2025-08-04 13:40:47] [SUCCESS] ✅ 创建空配置成功: Hong Kong 01 -> ID: fYAQQN94
[2025-08-04 13:40:47] [SUCCESS] ✅ 创建空配置成功: Hong Kong 02 -> ID: dcVC7ANx
[2025-08-04 13:40:47] [SUCCESS] ✅ 创建空配置成功: Hong Kong 03 -> ID: QSOjGtQP
[2025-08-04 13:40:48] [SUCCESS] ✅ 创建空配置成功: Hong Kong 04 -> ID: KcG7eL3c
[2025-08-04 13:40:48] [SUCCESS] ✅ 创建空配置成功: Hong Kong 05 -> ID: dT2ITxcR
[2025-08-04 13:40:49] [SUCCESS] ✅ 创建空配置成功: Hong Kong 06 -> ID: o12sW1lZ
[2025-08-04 13:40:49] [SUCCESS] ✅ 创建空配置成功: Hong Kong 07 -> ID: PKlmQAHl
[2025-08-04 13:40:50] [SUCCESS] ✅ 创建空配置成功: Hong Kong 08 -> ID: wzXgdQez
[2025-08-04 13:40:50] [SUCCESS] ✅ 创建空配置成功: Hong Kong 09 -> ID: i249J0uQ
[2025-08-04 13:40:51] [SUCCESS] ✅ 创建空配置成功: Hong Kong 10 -> ID: jfc0O2AA
[2025-08-04 13:40:51] [SUCCESS] ✅ 创建空配置成功: Hong Kong 11 -> ID: I2G3pM5w
[2025-08-04 13:40:52] [SUCCESS] ✅ 创建空配置成功: Hong Kong 12 -> ID: O2kIySun
[2025-08-04 13:40:53] [SUCCESS] ✅ 创建空配置成功: Hong Kong 13 -> ID: Xtt4sj6P
[2025-08-04 13:40:53] [SUCCESS] ✅ 创建空配置成功: Hong Kong 14 -> ID: uIWnRrgm
[2025-08-04 13:40:54] [SUCCESS] ✅ 创建空配置成功: Hong Kong 15 -> ID: uGGHWdeb
[2025-08-04 13:40:55] [SUCCESS] ✅ 创建空配置成功: Hong Kong 16 -> ID: FTb7aegv
[2025-08-04 13:40:56] [SUCCESS] ✅ 创建空配置成功: Hong Kong 17 -> ID: 0NoLO6Ti
[2025-08-04 13:40:56] [SUCCESS] ✅ 创建空配置成功: Hong Kong 18 -> ID: QCK8z16r
[2025-08-04 13:40:57] [SUCCESS] ✅ 创建空配置成功: Hong Kong 19 -> ID: 4KxflGu5
[2025-08-04 13:40:58] [SUCCESS] ✅ 创建空配置成功: Hong Kong 20 [Premium] -> ID: QUzvbbgH
[2025-08-04 13:40:59] [SUCCESS] ✅ 创建空配置成功: Hong Kong 21 [Premium] -> ID: StpYneLU
[2025-08-04 13:41:00] [SUCCESS] ✅ 创建空配置成功: Hong Kong 22 [Premium] -> ID: elnyvzyA
[2025-08-04 13:41:01] [SUCCESS] ✅ 创建空配置成功: Hong Kong 23 -> ID: 74UEYgd8
[2025-08-04 13:41:02] [SUCCESS] ✅ 创建空配置成功: USA Seattle 01 -> ID: eBZeUHpr
[2025-08-04 13:41:03] [SUCCESS] ✅ 创建空配置成功: USA Seattle 02 -> ID: OK4ztxFA
[2025-08-04 13:41:04] [SUCCESS] ✅ 创建空配置成功: USA Seattle 03 -> ID: IeuiJx82
[2025-08-04 13:41:05] [SUCCESS] ✅ 创建空配置成功: USA Seattle 04 -> ID: 0tcVGCqh
[2025-08-04 13:41:06] [SUCCESS] ✅ 创建空配置成功: USA Seattle 05 -> ID: XYh7ddt8
[2025-08-04 13:41:08] [SUCCESS] ✅ 创建空配置成功: USA Seattle 06 -> ID: E7DNHsYq
[2025-08-04 13:41:09] [SUCCESS] ✅ 创建空配置成功: USA Seattle 07 -> ID: RQGXqkVt
[2025-08-04 13:41:10] [SUCCESS] ✅ 创建空配置成功: USA Seattle 08 -> ID: ZfMgj0tG
[2025-08-04 13:41:11] [SUCCESS] ✅ 创建空配置成功: USA Seattle 09 -> ID: UvNH973F
[2025-08-04 13:41:13] [SUCCESS] ✅ 创建空配置成功: USA San Jose 01 [Premium] -> ID: u6bh1Ulc
[2025-08-04 13:41:14] [SUCCESS] ✅ 创建空配置成功: USA San Jose 02 [Premium] -> ID: NdxzQYM3
[2025-08-04 13:41:16] [SUCCESS] ✅ 创建空配置成功: USA San Jose 03 [Premium] -> ID: P6d0gnek
[2025-08-04 13:41:17] [SUCCESS] ✅ 创建空配置成功: USA San Jose 04 [Premium] -> ID: 99DFT6ql
[2025-08-04 13:41:18] [SUCCESS] ✅ 创建空配置成功: USA San Jose 05 [Premium] -> ID: yG28vsJk
[2025-08-04 13:41:20] [SUCCESS] ✅ 创建空配置成功: USA San Jose 06 [Premium] -> ID: xaP2pUlz
[2025-08-04 13:41:22] [SUCCESS] ✅ 创建空配置成功: USA San Jose 07 -> ID: b2LnMaYw
[2025-08-04 13:41:23] [SUCCESS] ✅ 创建空配置成功: USA San Jose 08 -> ID: 3956QzqZ
[2025-08-04 13:41:25] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 01 -> ID: NUSqHLcR
[2025-08-04 13:41:27] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 02 -> ID: 5lQipXrF
[2025-08-04 13:41:28] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 03 -> ID: bfqqpzO9
[2025-08-04 13:41:30] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 04 -> ID: 5yZysgFs
[2025-08-04 13:41:32] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 05 -> ID: hyBxYD3W
[2025-08-04 13:41:34] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 06 -> ID: 53Ij9JZw
[2025-08-04 13:41:36] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 07 -> ID: YGt6sBY6
[2025-08-04 13:41:38] [SUCCESS] ✅ 创建空配置成功: Russia St. Petersburg -> ID: EzWRLK3u
[2025-08-04 13:41:40] [SUCCESS] ✅ 创建空配置成功: Russia Moscow 01 -> ID: gDQP5v75
[2025-08-04 13:41:42] [SUCCESS] ✅ 创建空配置成功: Austria 01 -> ID: RCeOagic
[2025-08-04 13:41:44] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 01 [Premium] -> ID: matt7Ljo
[2025-08-04 13:41:47] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 02 -> ID: DNr2eBdH
[2025-08-04 13:41:49] [SUCCESS] ✅ 创建空配置成功: Japan 01 -> ID: TY5z9GxX
[2025-08-04 13:41:51] [SUCCESS] ✅ 创建空配置成功: Japan 02 -> ID: WLwkYxI9
[2025-08-04 13:41:53] [SUCCESS] ✅ 创建空配置成功: Japan 03 -> ID: repB6kku
[2025-08-04 13:41:55] [SUCCESS] ✅ 创建空配置成功: Japan 04 -> ID: cud4n7wR
[2025-08-04 13:41:58] [SUCCESS] ✅ 创建空配置成功: Japan 05 -> ID: 6aGgnZyL
[2025-08-04 13:42:00] [SUCCESS] ✅ 创建空配置成功: Japan 06 -> ID: tMFlSfLi
[2025-08-04 13:42:02] [SUCCESS] ✅ 创建空配置成功: Japan 07 -> ID: MbzAt0jV
[2025-08-04 13:42:05] [SUCCESS] ✅ 创建空配置成功: Japan 08 -> ID: QSPtrMyY
[2025-08-04 13:42:07] [SUCCESS] ✅ 创建空配置成功: Japan 09 -> ID: KL826sgU
[2025-08-04 13:42:10] [SUCCESS] ✅ 创建空配置成功: Japan 10 -> ID: oWr0fSOB
[2025-08-04 13:42:12] [SUCCESS] ✅ 创建空配置成功: Japan 11 -> ID: nbseECCe
[2025-08-04 13:42:15] [SUCCESS] ✅ 创建空配置成功: Japan 12 -> ID: AQgC7Osn
[2025-08-04 13:42:18] [SUCCESS] ✅ 创建空配置成功: Japan 13 -> ID: i5Pdo0ov
[2025-08-04 13:42:20] [SUCCESS] ✅ 创建空配置成功: Japan 14 -> ID: QdjNlMhf
[2025-08-04 13:42:23] [SUCCESS] ✅ 创建空配置成功: Japan 15 -> ID: XmFhXA1W
[2025-08-04 13:42:26] [SUCCESS] ✅ 创建空配置成功: Japan 16 -> ID: xbb2C7jN
[2025-08-04 13:42:28] [INFO] 🔍 找到主配置ID: cfg013fd6
[2025-08-04 13:42:28] [INFO] 🔑 更新token: a046354b02db006f693b455e7cd395c3 -> a046354b02db006f693b455e7cd395c3
[2025-08-04 13:42:28] [INFO] 🔑 更新sessionid: eed132fe410860b4084464fac4472488 -> eed132fe410860b4084464fac4472488
[2025-08-04 13:42:28] [INFO] 💾 批量提交请求包已保存到: 批量提交请求包_20250804_134228.txt
[2025-08-04 13:42:31] [INFO] 💾 批量提交响应包已保存到: 批量提交响应包_20250804_134231.txt
[2025-08-04 13:42:31] [SUCCESS] ✅ 批量提交成功！已设置 135 个配置
[2025-08-04 13:47:09] [INFO] 开始登录OpenWrt后台

[2025-08-04 13:47:09] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 13:47:09] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=c2833fee16ff3e7a1286857feed1c7d5; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=c2833fee16ff3e7a1286857feed1c7d5; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: c2833fee16ff3e7a1286857feed1c7d5

响应内容:

==================================================

[2025-08-04 13:47:09] [SUCCESS] ✅ 登录成功！Session ID: c2833fee16ff3e7a1286857feed1c7d5

[2025-08-04 13:47:09] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:47:11] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-04 13:47:11] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点

[2025-08-04 13:47:11] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 13:47:12] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-04 13:47:12] [SUCCESS] 🔑 成功提取token: 98b1e3a87a47887736370ce36b7e3177
[2025-08-04 13:47:12] [SUCCESS] 📋 已存在的socks配置: ['fYAQQN94', 'GdlFL2gu', 'dcVC7ANx', 'kaTjuuUx', 'QSOjGtQP', 'yIIR4LlF', 'KcG7eL3c', 'SyecIcgb', 'dT2ITxcR', 'McIR3bDK', 'o12sW1lZ', 'Mpxzrl9d', 'PKlmQAHl', 'whLzLg9t', 'wzXgdQez', 'z99driwU', 'i249J0uQ', 'mSih4WWJ', 'jfc0O2AA', 'innhsg2Y', 'I2G3pM5w', '8sEM5dW5', 'O2kIySun', 'rJJnvhhn', 'Xtt4sj6P', '5yH3cKzH', 'uIWnRrgm', 'NRncDRVd', 'uGGHWdeb', '7G61WLYt', 'FTb7aegv', 'ZhjwRupc', '0NoLO6Ti', 'Egty89T3', 'QCK8z16r', 'G3QzKmAS', '4KxflGu5', '3shgG7k5', 'QUzvbbgH', 'HLnVXREy', 'StpYneLU', 'zl9CqMMo', 'elnyvzyA', 'MLYqaa5u', '74UEYgd8', 'YteNyevN', 'eBZeUHpr', 'X1tmndhj', 'OK4ztxFA', 'RD9TN0NV', 'IeuiJx82', '6jwuQnnr', '0tcVGCqh', 'fro7Mnsz', 'XYh7ddt8', 'uTLHlYF8', 'E7DNHsYq', 'ngCg6soD', 'RQGXqkVt', 'XZcR3DZd', 'ZfMgj0tG', 'qsmYoyJr', 'UvNH973F', 'n6nwDxlU', 'u6bh1Ulc', 'bngrGbfQ', 'NdxzQYM3', 'xXtpS8JO', 'P6d0gnek', '25VC5hxp', '99DFT6ql', 'X9Cwq9FY', 'yG28vsJk', '5IdC21MO', 'xaP2pUlz', '8ITp6UfQ', 'b2LnMaYw', 'AEXMIBTI', '3956QzqZ', '8wiGYIgx', 'NUSqHLcR', 'KV4gGzTd', '5lQipXrF', '7JrC1uEp', 'bfqqpzO9', 'vY285vlX', '5yZysgFs', '8dXh52cc', 'hyBxYD3W', 'uAYDsN91', '53Ij9JZw', 'vuYqoB5Z', 'YGt6sBY6', '53PRKp62', 'EzWRLK3u', 't2wlt88I', 'gDQP5v75', 'LPcYs0uL', 'RCeOagic', 'VfMX1PI2', 'matt7Ljo', 'voRQfrkX', 'DNr2eBdH', 'qLje9XeQ', 'TY5z9GxX', 'GAEmo0p8', 'WLwkYxI9', 'LxmKr07T', 'repB6kku', 'CgSaJCHR', 'cud4n7wR', 'n9SoE6fB', '6aGgnZyL', 'sUlI6tbl', 'tMFlSfLi', 'Z5aWP0tD', 'MbzAt0jV', 'lL7Nwk0R', 'QSPtrMyY', '3oRMuSq2', 'KL826sgU', 'tS7p9KX5', 'oWr0fSOB', 'OsEh3vMY', 'nbseECCe', '7VaHgJTt', 'AQgC7Osn', 'teYwASTg', 'i5Pdo0ov', 'DbP9IrLb', 'QdjNlMhf', 'B1qVtYQE', 'XmFhXA1W', 'E2vTroo4', 'xbb2C7jN', 'oAMsdSvD']
