OpenWrt Passwall 工具运行日志
==================================================

[2025-08-04 14:19:39] [INFO] 开始登录OpenWrt后台

[2025-08-04 14:19:39] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 14:19:40] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=e612fc28c62a1e7781dfe78e1f8a2079; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=e612fc28c62a1e7781dfe78e1f8a2079; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: e612fc28c62a1e7781dfe78e1f8a2079

响应内容:

==================================================

[2025-08-04 14:19:40] [SUCCESS] ✅ 登录成功！Session ID: e612fc28c62a1e7781dfe78e1f8a2079

[2025-08-04 14:19:40] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 14:19:42] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-04 14:19:42] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点

[2025-08-04 14:19:42] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 14:19:43] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-04 14:19:43] [SUCCESS] 🔑 成功提取token: f04019c3034511f6bbd35ccdf277e8f5
[2025-08-04 14:19:43] [SUCCESS] 📋 已存在的socks配置: ['fYAQQN94', 'GdlFL2gu', 'dcVC7ANx', 'kaTjuuUx', 'QSOjGtQP', 'yIIR4LlF', 'KcG7eL3c', 'SyecIcgb', 'dT2ITxcR', 'McIR3bDK', 'o12sW1lZ', 'Mpxzrl9d', 'PKlmQAHl', 'whLzLg9t', 'wzXgdQez', 'z99driwU', 'i249J0uQ', 'mSih4WWJ', 'jfc0O2AA', 'innhsg2Y', 'I2G3pM5w', '8sEM5dW5', 'O2kIySun', 'rJJnvhhn', 'Xtt4sj6P', '5yH3cKzH', 'uIWnRrgm', 'NRncDRVd', 'uGGHWdeb', '7G61WLYt', 'FTb7aegv', 'ZhjwRupc', '0NoLO6Ti', 'Egty89T3', 'QCK8z16r', 'G3QzKmAS', '4KxflGu5', '3shgG7k5', 'QUzvbbgH', 'HLnVXREy', 'StpYneLU', 'zl9CqMMo', 'elnyvzyA', 'MLYqaa5u', '74UEYgd8', 'YteNyevN', 'eBZeUHpr', 'X1tmndhj', 'OK4ztxFA', 'RD9TN0NV', 'IeuiJx82', '6jwuQnnr', '0tcVGCqh', 'fro7Mnsz', 'XYh7ddt8', 'uTLHlYF8', 'E7DNHsYq', 'ngCg6soD', 'RQGXqkVt', 'XZcR3DZd', 'ZfMgj0tG', 'qsmYoyJr', 'UvNH973F', 'n6nwDxlU', 'u6bh1Ulc', 'bngrGbfQ', 'NdxzQYM3', 'xXtpS8JO', 'P6d0gnek', '25VC5hxp', '99DFT6ql', 'X9Cwq9FY', 'yG28vsJk', '5IdC21MO', 'xaP2pUlz', '8ITp6UfQ', 'b2LnMaYw', 'AEXMIBTI', '3956QzqZ', '8wiGYIgx', 'NUSqHLcR', 'KV4gGzTd', '5lQipXrF', '7JrC1uEp', 'bfqqpzO9', 'vY285vlX', '5yZysgFs', '8dXh52cc', 'hyBxYD3W', 'uAYDsN91', '53Ij9JZw', 'vuYqoB5Z', 'YGt6sBY6', '53PRKp62', 'EzWRLK3u', 't2wlt88I', 'gDQP5v75', 'LPcYs0uL', 'RCeOagic', 'VfMX1PI2', 'matt7Ljo', 'voRQfrkX', 'DNr2eBdH', 'qLje9XeQ', 'TY5z9GxX', 'GAEmo0p8', 'WLwkYxI9', 'LxmKr07T', 'repB6kku', 'CgSaJCHR', 'cud4n7wR', 'n9SoE6fB', '6aGgnZyL', 'sUlI6tbl', 'tMFlSfLi', 'Z5aWP0tD', 'MbzAt0jV', 'lL7Nwk0R', 'QSPtrMyY', '3oRMuSq2', 'KL826sgU', 'tS7p9KX5', 'oWr0fSOB', 'OsEh3vMY', 'nbseECCe', '7VaHgJTt', 'AQgC7Osn', 'teYwASTg', 'i5Pdo0ov', 'DbP9IrLb', 'QdjNlMhf', 'B1qVtYQE', 'XmFhXA1W', 'E2vTroo4', 'xbb2C7jN', 'oAMsdSvD']

[2025-08-04 14:19:43] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
  Accept-Encoding: gzip, deflate
  Accept-Language: zh-CN,zh;q=0.9
  Cache-Control: max-age=0
  Origin: http://*************
  Referer: http://*************/cgi-bin/luci/admin/services/passwall2/settings
  Upgrade-Insecure-Requests: 1
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryACE5azrrYYWFcSp0
POST数据: multipart_data
------------------------------
[2025-08-04 14:19:43] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - åºæ¬è®¾ç½®
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-tou...
==================================================

[2025-08-04 14:19:43] [SUCCESS] ✅ 删除socks配置请求成功
[2025-08-04 14:19:44] [INFO] 💾 已更新nodes_data.json，共 135 个节点
[2025-08-04 14:19:44] [SUCCESS] ✅ 创建空配置成功: Hong Kong 01 -> ID: pj6qsyJV
[2025-08-04 14:19:44] [SUCCESS] ✅ 创建空配置成功: Hong Kong 02 -> ID: X2aC1tHm
[2025-08-04 14:19:45] [SUCCESS] ✅ 创建空配置成功: Hong Kong 03 -> ID: I9kcBX4m
[2025-08-04 14:19:45] [SUCCESS] ✅ 创建空配置成功: Hong Kong 04 -> ID: a5WpMN0n
[2025-08-04 14:19:45] [SUCCESS] ✅ 创建空配置成功: Hong Kong 05 -> ID: skCbt3FO
[2025-08-04 14:19:46] [SUCCESS] ✅ 创建空配置成功: Hong Kong 06 -> ID: m543uoVs
[2025-08-04 14:19:46] [SUCCESS] ✅ 创建空配置成功: Hong Kong 07 -> ID: nKk1sPFz
[2025-08-04 14:19:46] [SUCCESS] ✅ 创建空配置成功: Hong Kong 08 -> ID: TYT51eTW
[2025-08-04 14:19:47] [SUCCESS] ✅ 创建空配置成功: Hong Kong 09 -> ID: ShHP4bsy
[2025-08-04 14:19:47] [SUCCESS] ✅ 创建空配置成功: Hong Kong 10 -> ID: fbeuy5IH
[2025-08-04 14:19:48] [SUCCESS] ✅ 创建空配置成功: Hong Kong 11 -> ID: 4twvs1wc
[2025-08-04 14:19:49] [SUCCESS] ✅ 创建空配置成功: Hong Kong 12 -> ID: Xf0Cu9kf
[2025-08-04 14:19:49] [SUCCESS] ✅ 创建空配置成功: Hong Kong 13 -> ID: IqQNbiOZ
[2025-08-04 14:19:50] [SUCCESS] ✅ 创建空配置成功: Hong Kong 14 -> ID: tMH4iLyg
[2025-08-04 14:19:51] [SUCCESS] ✅ 创建空配置成功: Hong Kong 15 -> ID: x7jzjbFS
[2025-08-04 14:19:51] [SUCCESS] ✅ 创建空配置成功: Hong Kong 16 -> ID: TzLjpEFi
[2025-08-04 14:19:52] [SUCCESS] ✅ 创建空配置成功: Hong Kong 17 -> ID: ul1o9bDD
[2025-08-04 14:19:53] [SUCCESS] ✅ 创建空配置成功: Hong Kong 18 -> ID: kWPwDLYO
[2025-08-04 14:19:54] [SUCCESS] ✅ 创建空配置成功: Hong Kong 19 -> ID: F1G4ThO8
[2025-08-04 14:19:55] [SUCCESS] ✅ 创建空配置成功: Hong Kong 20 [Premium] -> ID: elCnOkys
[2025-08-04 14:19:55] [SUCCESS] ✅ 创建空配置成功: Hong Kong 21 [Premium] -> ID: qL9X0uvY
[2025-08-04 14:19:56] [SUCCESS] ✅ 创建空配置成功: Hong Kong 22 [Premium] -> ID: qGUQqfAi
[2025-08-04 14:19:57] [SUCCESS] ✅ 创建空配置成功: Hong Kong 23 -> ID: OSA3Ju5r
[2025-08-04 14:19:58] [SUCCESS] ✅ 创建空配置成功: USA Seattle 01 -> ID: FHfHY9dX
[2025-08-04 14:19:59] [SUCCESS] ✅ 创建空配置成功: USA Seattle 02 -> ID: IvBLgxIi
[2025-08-04 14:20:01] [SUCCESS] ✅ 创建空配置成功: USA Seattle 03 -> ID: wWGrfnhV
[2025-08-04 14:20:02] [SUCCESS] ✅ 创建空配置成功: USA Seattle 04 -> ID: F33bOuKQ
[2025-08-04 14:20:03] [SUCCESS] ✅ 创建空配置成功: USA Seattle 05 -> ID: 8HpymD4c
[2025-08-04 14:20:04] [SUCCESS] ✅ 创建空配置成功: USA Seattle 06 -> ID: 5nVOpNgf
[2025-08-04 14:20:05] [SUCCESS] ✅ 创建空配置成功: USA Seattle 07 -> ID: IeVLVZaZ
[2025-08-04 14:20:06] [SUCCESS] ✅ 创建空配置成功: USA Seattle 08 -> ID: Dv8zaAZG
[2025-08-04 14:20:08] [SUCCESS] ✅ 创建空配置成功: USA Seattle 09 -> ID: Wr8y3rwc
[2025-08-04 14:20:09] [SUCCESS] ✅ 创建空配置成功: USA San Jose 01 [Premium] -> ID: 7EacX20H
[2025-08-04 14:20:11] [SUCCESS] ✅ 创建空配置成功: USA San Jose 02 [Premium] -> ID: Z9Bjo5KE
[2025-08-04 14:20:12] [SUCCESS] ✅ 创建空配置成功: USA San Jose 03 [Premium] -> ID: C8Yd3Lyk
[2025-08-04 14:20:13] [SUCCESS] ✅ 创建空配置成功: USA San Jose 04 [Premium] -> ID: czpvrXJp
[2025-08-04 14:20:15] [SUCCESS] ✅ 创建空配置成功: USA San Jose 05 [Premium] -> ID: yzuSTh9g
[2025-08-04 14:20:16] [SUCCESS] ✅ 创建空配置成功: USA San Jose 06 [Premium] -> ID: VHReB0kA
[2025-08-04 14:20:18] [SUCCESS] ✅ 创建空配置成功: USA San Jose 07 -> ID: qyuBrmxE
[2025-08-04 14:20:19] [SUCCESS] ✅ 创建空配置成功: USA San Jose 08 -> ID: 9RJ73119
[2025-08-04 14:20:21] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 01 -> ID: OTOuwdyy
[2025-08-04 14:20:23] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 02 -> ID: Rauwqzq4
[2025-08-04 14:20:25] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 03 -> ID: t3XQNXUK
[2025-08-04 14:20:26] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 04 -> ID: i9I2FVs5
[2025-08-04 14:20:28] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 05 -> ID: 8Tpz5KtT
[2025-08-04 14:20:30] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 06 -> ID: 3YTfhTBG
[2025-08-04 14:20:32] [SUCCESS] ✅ 创建空配置成功: USA Los Angeles 07 -> ID: 9nTY7LeL
[2025-08-04 14:20:34] [SUCCESS] ✅ 创建空配置成功: Russia St. Petersburg -> ID: eCCbOEQk
[2025-08-04 14:20:36] [SUCCESS] ✅ 创建空配置成功: Russia Moscow 01 -> ID: o0JnmAZ2
[2025-08-04 14:20:38] [SUCCESS] ✅ 创建空配置成功: Austria 01 -> ID: dn5R7SJV
[2025-08-04 14:20:40] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 01 [Premium] -> ID: HfUEDMZK
[2025-08-04 14:20:42] [SUCCESS] ✅ 创建空配置成功: Australia Sydney 02 -> ID: 4hEYqd20
[2025-08-04 14:20:44] [SUCCESS] ✅ 创建空配置成功: Japan 01 -> ID: k4oxmQ4e
[2025-08-04 14:20:46] [SUCCESS] ✅ 创建空配置成功: Japan 02 -> ID: n6UlaDvn
[2025-08-04 14:20:48] [SUCCESS] ✅ 创建空配置成功: Japan 03 -> ID: gDpRlHHl
[2025-08-04 14:20:51] [SUCCESS] ✅ 创建空配置成功: Japan 04 -> ID: E31VW1rY
[2025-08-04 14:20:53] [SUCCESS] ✅ 创建空配置成功: Japan 05 -> ID: xVPnfwx4
[2025-08-04 14:20:55] [SUCCESS] ✅ 创建空配置成功: Japan 06 -> ID: U8To5leY
[2025-08-04 14:20:58] [SUCCESS] ✅ 创建空配置成功: Japan 07 -> ID: JqkcRjmh
[2025-08-04 14:21:00] [SUCCESS] ✅ 创建空配置成功: Japan 08 -> ID: 2ijFsoKZ
[2025-08-04 14:21:03] [SUCCESS] ✅ 创建空配置成功: Japan 09 -> ID: foeazy4D
[2025-08-04 14:21:05] [SUCCESS] ✅ 创建空配置成功: Japan 10 -> ID: Gljtaogw
[2025-08-04 14:21:08] [SUCCESS] ✅ 创建空配置成功: Japan 11 -> ID: G5Sq0HTm
[2025-08-04 14:21:10] [SUCCESS] ✅ 创建空配置成功: Japan 12 -> ID: VoC7AZA3
[2025-08-04 14:21:13] [SUCCESS] ✅ 创建空配置成功: Japan 13 -> ID: N5nERdqD
[2025-08-04 14:21:16] [SUCCESS] ✅ 创建空配置成功: Japan 14 -> ID: BNFlkNCl
[2025-08-04 14:21:19] [SUCCESS] ✅ 创建空配置成功: Japan 15 -> ID: kqQZdiLW
[2025-08-04 14:21:21] [SUCCESS] ✅ 创建空配置成功: Japan 16 -> ID: TLgQzzbj
[2025-08-04 14:21:24] [INFO] 🔍 找到主配置ID: cfg013fd6
[2025-08-04 14:21:24] [INFO] 🔑 更新token: f04019c3034511f6bbd35ccdf277e8f5 -> f04019c3034511f6bbd35ccdf277e8f5
[2025-08-04 14:21:24] [INFO] 🔑 更新sessionid: e612fc28c62a1e7781dfe78e1f8a2079 -> e612fc28c62a1e7781dfe78e1f8a2079
[2025-08-04 14:21:24] [INFO] 💾 批量提交请求包已保存到: 批量提交请求包_20250804_142124.txt
[2025-08-04 14:21:26] [INFO] 💾 批量提交响应包已保存到: 批量提交响应包_20250804_142126.txt
[2025-08-04 14:21:26] [SUCCESS] ✅ 批量提交成功！已设置 135 个配置
[2025-08-04 14:22:17] [INFO] 开始登录OpenWrt后台

[2025-08-04 14:22:17] === HTTP请求 ===
方法: POST
URL: http://*************/cgi-bin/luci/
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
POST数据:
  luci_username: root
  luci_password: ********
------------------------------
[2025-08-04 14:22:17] === HTTP响应 登录请求 ===
状态码: 302
状态文本: Found
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  Set-Cookie: sysauth_http=32950eef004e58cf8ce6c96dd605c7f4; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  Location: /cgi-bin/luci/

🍪 重要Cookie信息:
  完整Cookie: sysauth_http=32950eef004e58cf8ce6c96dd605c7f4; path=/cgi-bin/luci/; SameSite=Strict; HttpOnly
  ✅ 提取到sysauth_http: 32950eef004e58cf8ce6c96dd605c7f4

响应内容:

==================================================

[2025-08-04 14:22:17] [SUCCESS] ✅ 登录成功！Session ID: 32950eef004e58cf8ce6c96dd605c7f4

[2025-08-04 14:22:17] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/node_list
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 14:22:19] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Node List
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-ico...
==================================================

[2025-08-04 14:22:19] [SUCCESS] ✅ 从节点列表页面成功获取到 135 个节点
[2025-08-04 14:22:19] [INFO] 💾 已更新nodes_data.json，共 135 个节点

[2025-08-04 14:22:19] === HTTP请求 ===
方法: GET
URL: http://*************/cgi-bin/luci/admin/services/passwall2/settings
请求头:
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  Accept-Encoding: gzip, deflate
  Accept: */*
  Connection: keep-alive
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
------------------------------
[2025-08-04 14:22:20] === HTTP响应  ===
状态码: 200
状态文本: OK
响应头:
  Connection: Keep-Alive
  Transfer-Encoding: chunked
  Keep-Alive: timeout=20
  X-CBI-State: 0
  Content-Type: text/html
  Cache-Control: no-cache
  Expires: 0
  X-Frame-Options: SAMEORIGIN
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff

响应内容（前1000字符）:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touc...
==================================================

[2025-08-04 14:22:20] [SUCCESS] 🔑 成功提取token: a427c86ed36e32f570fabdea8104fb20
[2025-08-04 14:22:20] [SUCCESS] 📋 已存在的socks配置: ['pj6qsyJV', 'LWWfUwcR', 'X2aC1tHm', 'WazMF6pu', 'I9kcBX4m', 'E3jCSlLV', 'a5WpMN0n', 'b5eQD4Jd', 'skCbt3FO', 'xpU1Yvtm', 'm543uoVs', '9TdC0lID', 'nKk1sPFz', 'rKnO8MMx', 'TYT51eTW', 'AyoF9BWP', 'ShHP4bsy', 'AwYbeUs8', 'fbeuy5IH', '7GZA41pl', '4twvs1wc', 'E2TwwJSI', 'Xf0Cu9kf', '2WlhpOFh', 'IqQNbiOZ', 'dGVdSvNg', 'tMH4iLyg', 'jBYQViJV', 'x7jzjbFS', 'LiFScmKB', 'TzLjpEFi', '18lmCt9m', 'ul1o9bDD', 'd6jbzrNF', 'kWPwDLYO', 'LOngSaCg', 'F1G4ThO8', 'xZKuuqtJ', 'elCnOkys', 'aFAdouoC', 'qL9X0uvY', '2zPvbEMh', 'qGUQqfAi', 'M49OblB5', 'OSA3Ju5r', 'ENluEdoh', 'FHfHY9dX', '9pyndXbF', 'IvBLgxIi', 'lnWUQmCN', 'wWGrfnhV', 'FPNOtWTG', 'F33bOuKQ', 'ESsjVrhF', '8HpymD4c', 'Ad52cPfF', '5nVOpNgf', 'yncJMERa', 'IeVLVZaZ', 'xgoue4lK', 'Dv8zaAZG', 'OS4BHEbD', 'Wr8y3rwc', '743IpTA1', '7EacX20H', 'cRyqIUcq', 'Z9Bjo5KE', 'UvQAWJhK', 'C8Yd3Lyk', '8Mmb4q7C', 'czpvrXJp', 'vsdWuXM4', 'yzuSTh9g', 'v5G6OWAE', 'VHReB0kA', 'MWB6kZMO', 'qyuBrmxE', 'J3tvJhAU', '9RJ73119', 'THJtDdQl', 'OTOuwdyy', 'QVM7jW5E', 'Rauwqzq4', '1VgdUXw9', 't3XQNXUK', 'PEdd48iL', 'i9I2FVs5', 'DFJ4I5Nn', '8Tpz5KtT', '35x31ZEo', '3YTfhTBG', 'dOMwrxOj', '9nTY7LeL', 'ejEzjlGB', 'eCCbOEQk', 'yb9EF3L9', 'o0JnmAZ2', '0TSaLsW5', 'dn5R7SJV', 'HZJsJolO', 'HfUEDMZK', 'XOHWJ7Of', '4hEYqd20', 'VjD4KjUy', 'k4oxmQ4e', 'AZgqFfEd', 'n6UlaDvn', '1giVcMPt', 'gDpRlHHl', 'oUpPrSqi', 'E31VW1rY', 'NQDxrIh3', 'xVPnfwx4', '5mLlq5fO', 'U8To5leY', 'SzZER9lh', 'JqkcRjmh', 'i8vitv4c', '2ijFsoKZ', 'oCBWbByA', 'foeazy4D', 'FoJiWVli', 'Gljtaogw', 'yAVPTZZZ', 'G5Sq0HTm', 'rxsGUiNI', 'VoC7AZA3', '1u8eUUhU', 'N5nERdqD', 'jzR4fE94', 'BNFlkNCl', 'DvOh68ls', 'kqQZdiLW', 'fb5Q3r1Z', 'TLgQzzbj', 'CplKLuQU']
