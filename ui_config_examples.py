#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI配置示例文件
提供常用的配置组合示例，可以直接复制到 ui_config.py 中使用
"""

# ==================== 配置示例 1：开发调试模式 ====================
"""
开发调试模式：启用所有调试文件保存，方便开发时查看请求响应包

复制以下方法到 ui_config.py 中替换对应方法：
"""

def get_save_create_files_default_debug():
    """开发调试模式：保存配置创建请求包和响应包"""
    return True

def get_save_delete_files_default_debug():
    """开发调试模式：保存配置删除请求包"""
    return True

def get_save_token_files_default_debug():
    """开发调试模式：保存token页面响应"""
    return True

def get_save_batch_files_default_debug():
    """开发调试模式：保存批量配置提交请求包和响应包"""
    return True

# ==================== 配置示例 2：测试模式 ====================
"""
测试模式：限制处理6个节点，方便快速测试

复制以下方法到 ui_config.py 中替换对应方法：
"""

def get_node_limit_default_test():
    """测试模式：限制处理6个节点"""
    return "6"

def get_processing_mode_default_test():
    """测试模式：使用限制模式"""
    return "limit"

# ==================== 配置示例 3：批量生产模式 ====================
"""
批量生产模式：每批20个节点，禁用调试文件，启用二次登录

复制以下方法到 ui_config.py 中替换对应方法：
"""

def get_processing_mode_default_batch():
    """批量生产模式：使用分批模式"""
    return "batch"

def get_batch_size_default_batch():
    """批量生产模式：每批20个节点"""
    return "20"

def get_retry_login_default_batch():
    """批量生产模式：启用二次登录重新提交"""
    return True

def get_save_create_files_default_batch():
    """批量生产模式：禁用调试文件"""
    return False

def get_save_delete_files_default_batch():
    """批量生产模式：禁用调试文件"""
    return False

def get_save_token_files_default_batch():
    """批量生产模式：禁用调试文件"""
    return False

def get_save_batch_files_default_batch():
    """批量生产模式：禁用调试文件"""
    return False

# ==================== 配置示例 4：紧凑窗口模式 ====================
"""
紧凑窗口模式：适合小屏幕使用

复制以下方法到 ui_config.py 中替换对应方法：
"""

def get_window_geometry_compact():
    """紧凑窗口模式：较小的窗口尺寸"""
    return "500x700"

def get_socks_text_height_compact():
    """紧凑窗口模式：较小的Socks配置文本框"""
    return 6

def get_log_text_height_compact():
    """紧凑窗口模式：较小的日志文本框"""
    return 6

# ==================== 配置示例 5：大窗口模式 ====================
"""
大窗口模式：适合大屏幕使用

复制以下方法到 ui_config.py 中替换对应方法：
"""

def get_window_geometry_large():
    """大窗口模式：较大的窗口尺寸"""
    return "600x1000"

def get_socks_text_height_large():
    """大窗口模式：较大的Socks配置文本框"""
    return 10

def get_log_text_height_large():
    """大窗口模式：较大的日志文本框"""
    return 12

# ==================== 配置示例 6：快速测试模式 ====================
"""
快速测试模式：限制3个节点，禁用二次登录，禁用所有调试文件

复制以下方法到 ui_config.py 中替换对应方法：
"""

def get_node_limit_default_quick():
    """快速测试模式：限制3个节点"""
    return "3"

def get_processing_mode_default_quick():
    """快速测试模式：使用限制模式"""
    return "limit"

def get_retry_login_default_quick():
    """快速测试模式：禁用二次登录"""
    return False

def get_save_create_files_default_quick():
    """快速测试模式：禁用调试文件"""
    return False

def get_save_delete_files_default_quick():
    """快速测试模式：禁用调试文件"""
    return False

def get_save_token_files_default_quick():
    """快速测试模式：禁用调试文件"""
    return False

def get_save_batch_files_default_quick():
    """快速测试模式：禁用调试文件"""
    return False

# ==================== 使用说明 ====================
"""
使用方法：

1. 选择您需要的配置模式
2. 复制对应的方法到 ui_config.py 文件中
3. 替换原有的同名方法
4. 保存文件并重新运行程序

例如，要启用开发调试模式：

1. 打开 ui_config.py 文件
2. 找到以下方法：
   - get_save_create_files_default()
   - get_save_delete_files_default()
   - get_save_token_files_default()
   - get_save_batch_files_default()

3. 将它们的返回值改为 True：
   
   @staticmethod
   def get_save_create_files_default():
       return True  # 改为 True
   
   @staticmethod
   def get_save_delete_files_default():
       return True  # 改为 True
   
   @staticmethod
   def get_save_token_files_default():
       return True  # 改为 True
   
   @staticmethod
   def get_save_batch_files_default():
       return True  # 改为 True

4. 保存文件，重新运行程序即可看到效果

混合配置：
您也可以混合使用不同的配置，例如：
- 使用大窗口模式的窗口尺寸
- 使用测试模式的节点限制
- 使用批量模式的处理方式
- 启用部分调试文件

只需要修改对应的方法返回值即可。
"""
