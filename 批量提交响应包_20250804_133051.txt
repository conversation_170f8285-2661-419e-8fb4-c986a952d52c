批量提交响应结果:
==================================================
状态码: 403
状态: Forbidden
配置数量: 1
配置ID列表: YsNYn79o
==================================================
响应头:
Connection: Keep-Alive
Transfer-Encoding: chunked
Keep-Alive: timeout=20
Content-Type: text/html
Cache-Control: no-cache
Expires: 0
X-Frame-Options: SAMEORIGIN
X-XSS-Protection: 1; mode=block
X-Content-Type-Options: nosniff

响应内容:
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>
        iStoreOS - Basic Settings
        - LuCI</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="full-screen" content="yes">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="application-name" content="iStoreOS - LuCI">
    <meta name="apple-mobile-web-app-title" content="iStoreOS - LuCI">
    <link rel="apple-touch-icon" sizes="60x60" href="/luci-static/argon/icon/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/luci-static/argon/icon/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/luci-static/argon/icon/apple-icon-144x144.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/luci-static/argon/icon/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/luci-static/argon/icon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/luci-static/argon/icon/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/luci-static/argon/icon/favicon-16x16.png">
    <link rel="manifest" href="/luci-static/argon/icon/manifest.json"  crossorigin="use-credentials">
    <meta name="msapplication-TileColor" content="#5e72e4">
    <meta name="msapplication-TileImage" content="/luci-static/argon/icon/ms-icon-144x144.png">
    <meta name="theme-color" content="#5e72e4">
    <link rel="stylesheet" href="/luci-static/argon/css/cascade.css?v=*********">
    <style title="text/css">
        
        :root {
            --primary: #5e72e4;
            --dark-primary: #483d8b;
            --blur-radius:10px;
            --blur-opacity:0.5;
            --blur-radius-dark:10px;
            --blur-opacity-dark:0.5;
        }
        </style>
	<link rel="shortcut icon" href="/luci-static/argon/favicon.ico">
<link rel="stylesheet" href="/luci-static/resources/easepi/easeicon.css?t=1649313193968">
	<script src="/luci-static/argon/js/polyfill.min.js?v=*********"></script>
	<script src="/cgi-bin/luci/admin/translations/en?v=git-24.339.46321-5f9267c"></script>
	<script src="/luci-static/resources/cbi.js?v=git-24.339.46321-5f9267c"></script>
	<script src="/luci-static/resources/luci.js?v=git-24.339.46321-5f9267c"></script>
	<script src="/luci-static/argon/js/jquery.min.js?v=3.5.1"></script>
</head>

<body
	theme="light"
	class="lang_en Basic Settings logged-in"
	data-page="admin-services-passwall2-settings">

	<div class="main">
		<div class="main-left" id="mainmenu" style="display:none">
			<div class="sidenav-header d-flex align-items-center">
				<a class="brand" href="#">iStoreOS</a>
				<div class="ml-auto">
					<!-- Sidenav toggler -->
					<div class="sidenav-toggler d-none d-xl-block active" data-action="sidenav-unpin"
						data-target="#sidenav-main">
						<div class="sidenav-toggler-inner">
							<i class="sidenav-toggler-line"></i>
							<i class="sidenav-toggler-line"></i>
							<i class="sidenav-toggler-line"></i>
						</div>
					</div>
				</div>
			</div>
			<div style="display: flex; flex-direction: column;position: relative;align-items: center;">
				<input id="menu_search" type="text" placeholder="Search Menu">
				<div id="search_result" style="display: none;">
					<ul class="float-list" style="width: 90%;min-height: 10em;max-height: 20em;"></ul>
				</div>
			</div>
		</div>
		<div class="main-right">
			<header class="bg-primary">
				<div class="fill">
					<div class="container">
						<div class="flex1">
							<a class="showSide"></a>
							<a class="brand" href="#">iStoreOS</a>
						</div>
						<div class="status" id="indicators"></div>
					</div>
				</div>
			</header>
			<div class="darkMask"></div>
			<div id="maincontent">
				<div class="container"><noscript>
						<div class="alert-message error">
							<h4>JavaScript required!</h4>
							<p>You must enable JavaScript in your browser or LuCI will not work properly.</p>
						</div>
					</noscript>

					<div id="tabmenu" style="display:none"></div>


<script type="text/javascript" src="/luci-static/resources/promis.min.js?v=git-24.339.46321-5f9267c"></script>
<script type="text/javascript" src="/luci-static/resources/luci.js?v=git-24.339.46321-5f9267c"></script>
<script type="text/javascript">
	L = new LuCI({"apply_rollback":30,"resource":"\/luci-static\/resources","media":"\/luci-static\/argon","documentroot":"\/www","pathinfo":"\/admin\/services\/passwall2\/settings","apply_display":1.5,"requestpath":["admin","services","passwall2","settings"],"ubuspath":"\/ubus\/","scriptname":"\/cgi-bin\/luci","dispatchpath":["admin","services","passwall2","settings"],"sessionid":"473d9a1c0c665facb90d413d32d3073e","token":"0749a256b21153c19c2297d1fd7c1594","apply_timeout":5,"apply_holdoff":2,"nodespec":{"satisfied":true,"action":{"post":{"cbi.submit":true},"type":"cbi","path":"passwall2\/client\/global"},"order":1,"readonly":false,"title":"Basic Settings"},"pollinterval":5});
</script>


<h2 name="content">Form token mismatch</h2>
<br />

<p class="alert-message">The submitted security token is invalid or already expired!</p>

<p>
	In order to prevent unauthorized access to the system, your request has
	been blocked. Click &#34;Continue Â»&#34; below to return to the previous page.
</p>

<hr />

<p class="right">
	<strong><a href="#" onclick="window.history.back();">Continue Â»</a></strong>
</p>


</div>
<footer class="mobile-hide">
	<div>
		<a class="luci-link" href="https://github.com/openwrt/luci">Powered by LuCI istoreos-22.03 branch (git-24.339.46321-5f9267c)</a> /
						<a href="https://github.com/jerrykuku/luci-theme-argon">ArgonTheme v*********</a> /
						iStoreOS 22.03.7 2024122712
		<ul class="breadcrumb pull-right" id="modemenu" style="display:none"></ul>
	</div>
</footer>
</div>
</div>
<script>
	// thanks for Jo-Philipp Wich <<EMAIL>>
	var luciLocation = ["admin","services","passwall2","settings"];
	var winHeight = $(window).height();
	$(window).resize(function () {
		var winWidth = $(window).width()
		if(winWidth < 600){
			var newHeight = $(this).height();
			var keyboradHeight = newHeight - winHeight;
			$(".ftc").css("bottom", keyboradHeight + 30);
		}
	})
</script>
<script type="text/javascript">L.require('menu-argon', null, '*********')</script>
</body>
</html>


