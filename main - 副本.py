#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenWrt Passwall Socks 批量配置工具
主程序入口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime
from openwrt_client import OpenWrtClient
from config import Config
from logger import request_logger

class PasswallSocksApp:
    def __init__(self, root):
        self.root = root
        self.root.title("OpenWrt Passwall Socks 批量配置工具")
        self.root.geometry("500x600")
        self.root.resizable(False, False)
        
        # 初始化客户端
        self.client = None
        self.is_running = False
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        # 标题
        title_frame = tk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        
        title_label = tk.Label(title_frame, text="OpenWrt Passwall Socks 批量配置工具", 
                              font=("Arial", 14, "bold"))
        title_label.pack()
        
        # 分隔线
        separator1 = ttk.Separator(self.root, orient='horizontal')
        separator1.pack(fill=tk.X, padx=10, pady=5)
        
        # 配置输入区域
        config_frame = tk.Frame(self.root)
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # OpenWrt地址
        tk.Label(config_frame, text="OpenWrt地址:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.host_entry = tk.Entry(config_frame, width=30)
        self.host_entry.insert(0, Config.DEFAULT_HOST)
        self.host_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 用户名
        tk.Label(config_frame, text="用户名:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.username_entry = tk.Entry(config_frame, width=30)
        self.username_entry.insert(0, Config.DEFAULT_USERNAME)
        self.username_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 密码
        tk.Label(config_frame, text="密码:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.password_entry = tk.Entry(config_frame, width=30, show="*")
        self.password_entry.insert(0, Config.DEFAULT_PASSWORD)
        self.password_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 测试选项
        test_frame = tk.Frame(self.root)
        test_frame.pack(fill=tk.X, padx=10, pady=5)

        self.test_delete_only = tk.BooleanVar()
        test_checkbox = tk.Checkbutton(test_frame, text="仅测试删除功能（删除现有配置后停止）",
                                      variable=self.test_delete_only,
                                      font=("Arial", 10))
        test_checkbox.pack(anchor=tk.W)

        # 开始按钮
        button_frame = tk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_button = tk.Button(button_frame, text="开始批量配置", 
                                     command=self.start_batch_config,
                                     bg="#4CAF50", fg="white", 
                                     font=("Arial", 12, "bold"),
                                     height=2)
        self.start_button.pack()
        
        # 日志显示区域
        log_frame = tk.Frame(self.root)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        tk.Label(log_frame, text="操作日志:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        
        # 创建日志文本框和滚动条
        log_text_frame = tk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.log_text = tk.Text(log_text_frame, height=15, wrap=tk.WORD, 
                               font=("Consolas", 9))
        scrollbar = tk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 进度条
        progress_frame = tk.Frame(self.root)
        progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(progress_frame, text="进度:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        
        self.progress_var = tk.StringVar()
        self.progress_var.set("等待开始...")
        self.progress_label = tk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.pack(anchor=tk.W, pady=2)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=2)
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
        log_entry = f"{timestamp} {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def update_progress(self, current, total, message=""):
        """更新进度条"""
        if total > 0:
            percentage = int((current / total) * 100)
            self.progress_bar['value'] = percentage
            
            # 创建进度条显示
            filled = int(percentage / 10)
            empty = 10 - filled
            progress_bar_text = "█" * filled + "░" * empty
            
            if message:
                self.progress_var.set(f"{message}: {progress_bar_text} {percentage}% ({current}/{total})")
            else:
                self.progress_var.set(f"进度: {progress_bar_text} {percentage}% ({current}/{total})")
        else:
            self.progress_var.set("等待开始...")
            self.progress_bar['value'] = 0
            
        self.root.update_idletasks()
        
    def validate_inputs(self):
        """验证输入参数"""
        host = self.host_entry.get().strip()
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not host:
            messagebox.showerror("输入错误", "请输入OpenWrt地址！")
            self.host_entry.focus()
            return False

        if not username:
            messagebox.showerror("输入错误", "请输入用户名！")
            self.username_entry.focus()
            return False

        if not password:
            messagebox.showerror("输入错误", "请输入密码！")
            self.password_entry.focus()
            return False

        # 验证IP地址格式
        if not self.is_valid_host(host):
            result = messagebox.askyesno("地址格式",
                                       f"地址 '{host}' 格式可能不正确，是否继续？")
            if not result:
                self.host_entry.focus()
                return False

        return True

    def is_valid_host(self, host):
        """验证主机地址格式"""
        import re
        # 简单的IP地址或域名验证
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'

        return re.match(ip_pattern, host) or re.match(domain_pattern, host)

    def start_batch_config(self):
        """开始批量配置"""
        if self.is_running:
            messagebox.showwarning("警告", "配置正在进行中，请等待完成！")
            return

        # 验证输入
        if not self.validate_inputs():
            return

        # 获取输入参数
        host = self.host_entry.get().strip()
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        # 确认操作
        if self.test_delete_only.get():
            confirm_message = (f"即将连接到 {host} 并测试删除功能。\n\n"
                             "此操作将：\n"
                             "1. 删除所有现有Socks配置\n"
                             "2. 删除完成后停止执行（不创建新配置）\n\n"
                             "是否继续？")
        else:
            confirm_message = (f"即将连接到 {host} 并批量配置Socks代理。\n\n"
                             "此操作将：\n"
                             "1. 删除所有现有Socks配置\n"
                             "2. 为每个节点创建新的Socks配置\n\n"
                             "是否继续？")

        result = messagebox.askyesno("确认操作", confirm_message)
        if not result:
            return

        # 禁用开始按钮并更改文本
        self.start_button.config(state=tk.DISABLED, text="配置中...", bg="#FF9800")
        self.is_running = True

        # 清空日志
        self.log_text.delete(1.0, tk.END)
        request_logger.clear_log()  # 清空文件日志
        self.update_progress(0, 0)

        # 在新线程中执行配置
        thread = threading.Thread(target=self.batch_config_worker,
                                 args=(host, username, password))
        thread.daemon = True
        thread.start()
        
    def batch_config_worker(self, host, username, password):
        """批量配置工作线程"""
        success = False
        try:
            # 初始化客户端
            self.log_message("初始化连接...")
            self.client = OpenWrtClient(host, username, password)

            # 步骤1: 登录
            self.log_message("开始登录...")
            if not self.client.login():
                self.log_message("❌ 登录失败！请检查地址、用户名和密码")
                self.show_error_message("登录失败", "请检查OpenWrt地址、用户名和密码是否正确")
                return
            self.log_message("✅ 登录成功")

            # 步骤2: 获取节点列表
            self.log_message("获取节点列表...")
            nodes = self.client.get_nodes()
            if not nodes:
                self.log_message("❌ 未找到任何节点")
                self.show_error_message("节点获取失败", "未找到任何可用节点，请检查Passwall2配置")
                return
            self.log_message(f"✅ 找到{len(nodes)}个节点")

            # 显示节点信息
            for node in nodes:
                node_info = f"  - {node['name']} ({node['region']})"
                if 'type' in node:
                    node_info += f" [{node['type']}]"
                if 'address' in node and 'port' in node:
                    node_info += f" {node['address']}:{node['port']}"
                self.log_message(node_info)

            # 步骤3: 获取token和已存在的socks配置
            self.log_message("获取token和socks配置...")
            token, existing_socks = self.client.get_token_and_socks_config()

            if not token:
                self.log_message("❌ 获取token失败")
                self.show_error_message("Token获取失败", "无法获取必要的token，请检查登录状态")
                return

            # 🔧 重要：将获取到的token赋值给client对象
            self.client.token = token
            self.log_message(f"🔑 已更新client token: {token}")

            self.log_message(f"✅ 找到{len(existing_socks)}个现有Socks配置")

            # 步骤4: 删除现有配置
            if existing_socks:
                self.log_message("开始删除现有Socks配置...")
                if self.client.delete_existing_socks(existing_socks):
                    self.log_message("✅ 删除现有配置成功")

                    # 检查是否只测试删除功能
                    if self.test_delete_only.get():
                        self.log_message("🧪 删除功能测试完成！")
                        self.log_message("📋 测试结果：成功删除了所有现有Socks配置")
                        self.log_message("🛑 按用户要求，删除测试完成后停止执行")
                        success = True
                        return
                else:
                    self.log_message("❌ 删除现有配置失败")
                    self.show_error_message("删除失败", "删除现有Socks配置失败，请手动检查")
                    return
            else:
                self.log_message("无现有Socks配置需要删除")

                # 如果没有现有配置但用户选择了仅测试删除功能
                if self.test_delete_only.get():
                    self.log_message("🧪 删除功能测试完成！")
                    self.log_message("📋 测试结果：没有找到需要删除的Socks配置")
                    self.log_message("🛑 按用户要求，删除测试完成后停止执行")
                    success = True
                    return

            # 步骤5: 为所有节点分配端口
            self.log_message("开始为节点分配端口...")
            nodes = self.client.assign_ports_to_nodes(nodes)
            self.log_message(f"✅ 端口分配完成，共 {len(nodes)} 个节点")

            # 步骤6: 测试创建6个Socks配置
            self.log_message("🧪 测试模式：创建前6个节点的Socks配置")

            if len(nodes) >= 6:
                test_nodes = nodes[:6]  # 取前6个节点进行测试
                created_count = 0

                for i, test_node in enumerate(test_nodes):
                    port = test_node['socks_port']

                    self.log_message(f"🧪 测试创建配置 {i+1}/6: {test_node['name']} ({test_node['region']}) -> 端口 {port}")

                    try:
                        if self.client.create_socks_config(test_node, port):
                            created_count += 1
                            self.log_message(f"✅ 测试成功！配置已创建: {test_node['name']} -> 端口 {port}")
                            self.update_progress(i + 1, 6, f"已创建 {created_count}/6 个测试配置")
                        else:
                            self.log_message(f"❌ 测试失败！创建配置失败: {test_node['name']}")
                            self.show_error_message("测试失败", f"创建节点 {test_node['name']} 的Socks配置失败")
                            return

                    except Exception as e:
                        self.log_message(f"❌ 测试异常: {test_node['name']} - {e}")
                        self.show_error_message("测试异常", f"创建节点 {test_node['name']} 时发生异常: {e}")
                        return

                self.log_message(f"🧪 6节点测试完成！成功创建 {created_count}/6 个配置")
                success = True
            else:
                self.log_message("❌ 节点数量不足6个，无法进行测试")
                return

            # self.log_message("开始创建Socks配置...")
            # total_nodes = len(nodes)
            # success_count = 0

            # for i, node in enumerate(nodes):
            #     self.update_progress(i, total_nodes, "创建Socks配置")

            #     port = self.client.get_port_for_region(node.get('region', ''))
            #     self.log_message(f"为节点 {node['name']} 创建Socks配置 (端口: {port})")

            #     if self.client.create_socks_config(node, port):
            #         self.log_message(f"✅ 节点 {node['name']} 配置成功")
            #         success_count += 1
            #     else:
            #         self.log_message(f"❌ 节点 {node['name']} 配置失败")
            #         self.show_error_message("配置失败", f"节点 {node['name']} 配置失败，流程已停止")
            #         return

            #     time.sleep(0.5)  # 避免请求过快

            # self.update_progress(total_nodes, total_nodes, "配置完成")
            # self.log_message(f"🎉 所有Socks配置创建完成！成功配置 {success_count}/{total_nodes} 个节点")
            # success = True

        except ConnectionError as e:
            error_msg = f"网络连接错误: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.show_error_message("连接错误", "无法连接到OpenWrt设备，请检查网络连接和设备地址")

        except TimeoutError as e:
            error_msg = f"请求超时: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.show_error_message("超时错误", "请求超时，请检查网络连接或稍后重试")

        except Exception as e:
            error_msg = f"配置过程中发生未知错误: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.show_error_message("未知错误", f"发生未知错误：{str(e)}")

        finally:
            # 恢复按钮状态
            def restore_button():
                self.start_button.config(state=tk.NORMAL, text="开始批量配置", bg="#4CAF50")
                self.is_running = False
            self.root.after(0, restore_button)

            if success:
                self.show_success_message("配置完成", "所有Socks配置已成功创建！")

    def show_error_message(self, title, message):
        """显示错误消息"""
        def show_msg():
            messagebox.showerror(title, message)
        self.root.after(0, show_msg)

    def show_success_message(self, title, message):
        """显示成功消息"""
        def show_msg():
            messagebox.showinfo(title, message)
        self.root.after(0, show_msg)

def main():
    root = tk.Tk()
    app = PasswallSocksApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
