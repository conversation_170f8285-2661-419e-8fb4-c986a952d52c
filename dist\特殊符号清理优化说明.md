# 特殊符号清理优化说明

## 📋 优化概述

已成功优化代码，在获取节点后自动去掉节点名称中的特殊符号（如 º、µ、°、± 等），然后保存到 nodes_data.json 中。

## 🔧 修改内容

### 1. 修改文件：`html_parser.py`

在 `_clean_node_name` 方法中添加了特殊符号清理功能：

```python
# 3. 去掉特殊符号（如 º、µ、°、±、²、³、¹、¼、½、¾、×、÷ 等）
special_symbols = [
    'º', 'µ', '°', '±', '²', '³', '¹', '¼', '½', '¾', '×', '÷',
    'ª', '«', '»', '¬', '®', '¯', '´', '¸', '¿', 'À', 'Á', 'Â', 'Ã',
    'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï',
    'Ð', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'Ù', 'Ú', 'Û', 'Ü',
    'Ý', 'Þ', 'ß', 'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è',
    'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ð', 'ñ', 'ò', 'ó', 'ô',
    'õ', 'ö', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'þ', 'ÿ'
]

for symbol in special_symbols:
    cleaned_name = cleaned_name.replace(symbol, '')
```

### 2. 清理流程

节点名称清理现在按以下顺序进行：

1. **去掉常见前缀**：如 "Xray："、"V2ray Trojan："等
2. **去掉损坏的emoji编码**：如 ð­ð°
3. **去掉常见乱码字符**：如 ï¼ï½ï¾ï¿
4. **去掉特殊符号**：如 º、µ、°、± 等（新增功能）
5. **去掉开头的特殊符号**：保留正常的emoji、中文、字母、数字、方括号
6. **清理多余的空格**：规范化空格

## 🧪 测试验证

### 测试用例

创建了 `test_special_symbols.py` 测试脚本，包含10个测试用例：

| 测试 | 输入 | 期望输出 | 描述 |
|------|------|----------|------|
| 1 | `µ Japan 01` | `Japan 01` | 测试µ符号清理 |
| 2 | `º Hong Kong 02` | `Hong Kong 02` | 测试º符号清理 |
| 3 | `°Singapore 03` | `Singapore 03` | 测试°符号清理 |
| 4 | `±USA 04²` | `USA 04` | 测试±和²符号清理 |
| 5 | `Xray：µ Germany 05º` | `Germany 05` | 测试前缀和多个特殊符号清理 |
| 6 | `×Korea 06÷` | `Korea 06` | 测试×和÷符号清理 |
| 7 | `àáâãäå Netherlands 07` | `Netherlands 07` | 测试拉丁字符清理 |
| 8 | `µºº°±²³¹¼½¾×÷ Taiwan 08` | `Taiwan 08` | 测试多个特殊符号组合清理 |
| 9 | `V2ray Trojan：µ Canada 09º` | `Canada 09` | 测试复杂前缀和特殊符号清理 |
| 10 | `   µ   Australia   10   º   ` | `Australia 10` | 测试空格和特殊符号清理 |

### 测试结果

```
📊 测试总结:
  总测试数: 10
  成功数量: 10
  失败数量: 0
  成功率: 100.0%
🎉 所有测试通过！特殊符号清理功能正常工作。
```

## 📁 影响的文件

1. **html_parser.py** - 主要修改文件，添加特殊符号清理逻辑
2. **nodes_data.json** - 保存清理后的节点数据
3. **test_special_symbols.py** - 新增测试文件

## 🔄 工作流程

1. **获取节点数据** → `openwrt_client.py` 调用 `get_nodes()`
2. **解析HTML** → `html_parser.py` 的 `extract_nodes()` 方法
3. **清理节点名称** → `_clean_node_name()` 方法（包含新的特殊符号清理）
4. **保存到JSON** → `_save_nodes_to_json()` 方法保存清理后的数据

## ✅ 优化效果

- ✅ 自动去除节点名称中的特殊符号（º、µ、°、± 等）
- ✅ 保持原有的前缀清理和乱码处理功能
- ✅ 清理后的节点名称更加干净、规范
- ✅ 保存到 nodes_data.json 的数据已经是清理后的干净数据
- ✅ 通过了完整的测试验证，成功率100%

## 🚀 使用方法

优化后的代码会在以下情况自动清理特殊符号：

1. **获取节点时**：调用 `client.get_nodes()` 时自动清理
2. **创建配置时**：使用清理后的节点名称
3. **导出配置时**：导出的socks配置使用清理后的节点名称

无需额外操作，特殊符号清理功能已集成到现有工作流程中。

## 📝 注意事项

- 特殊符号清理不会影响节点的功能性，只是美化显示名称
- 原始的节点ID和其他技术参数保持不变
- 清理后的名称仍然保留有意义的文字内容（中文、英文、数字等）
- 支持emoji和正常的Unicode字符
