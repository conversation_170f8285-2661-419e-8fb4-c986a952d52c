#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本
检查依赖并启动应用程序
"""

import sys
import subprocess
import importlib.util

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = ['requests', 'bs4', 'lxml']
    missing_packages = []
    
    for package in required_packages:
        if package == 'bs4':
            # beautifulsoup4包导入时使用bs4
            spec = importlib.util.find_spec('bs4')
        else:
            spec = importlib.util.find_spec(package)
            
        if spec is None:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """安装缺失的依赖包"""
    for package in packages:
        if package == 'bs4':
            package = 'beautifulsoup4'
        print(f"正在安装 {package}...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"{package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"{package} 安装失败")
            return False
    return True

def main():
    print("OpenWrt Passwall Socks 批量配置工具")
    print("=" * 50)
    
    # 检查依赖
    print("检查依赖包...")
    missing = check_dependencies()
    
    if missing:
        print(f"缺少以下依赖包: {', '.join(missing)}")
        response = input("是否自动安装? (y/n): ")
        if response.lower() == 'y':
            if not install_dependencies(missing):
                print("依赖安装失败，程序退出")
                return
        else:
            print("请手动安装依赖包后再运行程序")
            print("pip install -r requirements.txt")
            return
    
    print("依赖检查完成，启动应用程序...")
    
    # 导入并启动主程序
    try:
        from main import main as app_main
        app_main()
    except ImportError as e:
        print(f"导入主程序失败: {e}")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
